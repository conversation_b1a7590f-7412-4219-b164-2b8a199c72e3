{"_from": "@vue/shared@3.4.27", "_id": "@vue/shared@3.4.27", "_inBundle": false, "_integrity": "sha512-DL3NmY2OFlqmYYrzp39yi3LDkKxa5vZVwxWdQ3rG0ekuWscHraeIbnI8t+aZK7qhYqEqWKTUdijadunb9pnrgA==", "_location": "/@vue/shared", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/shared@3.4.27", "name": "@vue/shared", "escapedName": "@vue%2fshared", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/", "/@vue/compiler-core", "/@vue/compiler-dom", "/@vue/compiler-sfc", "/@vue/compiler-ssr", "/@vue/reactivity", "/@vue/runtime-core", "/@vue/runtime-dom", "/@vue/server-renderer"], "_resolved": "https://registry.npmjs.org/@vue/shared/-/shared-3.4.27.tgz", "_shasum": "f05e3cd107d157354bb4ae7a7b5fc9cf73c63b50", "_spec": "@vue/shared@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "deprecated": false, "description": "internal utils shared across @vue packages", "exports": {".": {"types": "./dist/shared.d.ts", "node": {"production": "./dist/shared.cjs.prod.js", "development": "./dist/shared.cjs.js", "default": "./index.js"}, "module": "./dist/shared.esm-bundler.js", "import": "./dist/shared.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/shared#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/shared.esm-bundler.js", "name": "@vue/shared", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/shared"}, "sideEffects": false, "types": "dist/shared.d.ts", "version": "3.4.27"}