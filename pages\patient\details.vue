<template>
	<gui-page :fullPage="true" :isLoading="pageLoading" ref="guiPage">
		<view slot="gBody" class="gui-flex1 gui-flex gui-columns">
			<view class="gui-list gui-margin-top gui-padding">
				<view class="gui-list-items">
					<view class="gui-list-image ucenter-face gui-relative head_img">
						<image v-if="info.sex==2" class="gui-list-image ucenter-face-image "
							src="@/static/images/avatar-girl.png" mode="widthFix">
						</image>
						<image v-if="info.sex==1" class="gui-list-image ucenter-face-image "
							src="@/static/images/avatar-man.png" mode="widthFix">
						</image>
						<image v-if="info.sex==3" class="gui-list-image ucenter-face-image "
							src="@/static/images/weizhi.png" mode="widthFix">
						</image>
					</view>
					<view class="gui-list-body">
						<view class="gui-flex">
							<text class="gui-list-title-text gui-primary-color">{{info.name}}</text>
							<text v-if="info.sex && sexlits.length>0" class="gui-color-gray gui-list-title-text gui-primary-color ml-80">{{sexlits.find(i=>{return info.sex == i.dictCode}).dictLabel}}&ensp;/&ensp;{{info.age}}岁&ensp;/&ensp;{{info.tel}}</text>
						</view>
						<view class="gui-flex" style="flex-direction: column; line-height: 50rpx;">
							<text class="gui-list-title-text gui-primary-color">就诊卡：{{info.visitCardNum}}</text>
							<text class="gui-list-title-text gui-primary-color">身份证号：{{info.idCard && info.idCard.replace(/(\d{6})\d*([0-9a-zA-Z]{2})/,"$1******$2")}}</text>
						</view>
						<view class="gui-flex" v-show="tags.length>0">
							<gui-tags v-for="(item,index) in tags" :text="item" :key="index" bgClass="acolor" :size="22"
								:borderRadius="50"></gui-tags>
						</view>
					</view>
				</view>
				<view class="gui-flex">
					<view @click="openRenq" class="fs-24 px-15 py-10 gui-bold gui-border-radius ml-100" style="color:#7784eb ; border: 2rpx solid #7784eb;margin-top: -15rpx; width: 160rpx;">
						<text class="fs-28 mr-5">+</text>人群分组
					</view>
					<view class="gui-relative">
						<view @click="showMenu" class="fs-24 px-15 py-10 gui-bold gui-border-radius ml-30" style="color:#7784eb ; border: 2rpx solid #7784eb; margin-top: -15rpx; width: 160rpx;">
							<text class="fs-28 mr-5">+</text>下达干预
						</view>
						<view>
							<gui-popup-menu style="margin-left: -260rpx;" @hideMenu="hideMenu" ref="gracePopupMenu" :menuWidth="'660rpx'" :arrowDirection="'center'">
								<view class="gui-bg-white gui-flex gui-space-around" style="border-radius:8rpx; padding:15rpx;">
									<view class="gui-color-white fs-32 py-20 zhuti-bnt-bg px-50 gui-border-radius-large" @tap.stop="tap" data-id="1">问卷</view>
									<view class="gui-color-white fs-32 py-20 zhuti-bnt-bg px-50 gui-border-radius-large" @tap.stop="tap" data-id="3">宣教</view>
									<view class="gui-color-white fs-32 py-20 zhuti-bnt-bg px-50 gui-border-radius-large" @tap.stop="tap" data-id="2">监测</view>
								</view>
							</gui-popup-menu>
						</view>
					</view>
					<view @click="showFangan" class="fs-24 px-15 py-10 gui-bold gui-border-radius ml-30" style="color:#7784eb ; border: 2rpx solid #7784eb; margin-top: -15rpx; width: 160rpx;">
						<text class="fs-28 mr-5">+</text>下达方案
					</view>
				</view>
			</view>
			<!-- 局部选项卡 使用 切换导航 + swiper 实现 -->
			<view class="demo-nav">
				<gui-switch-navigation :items="tabs" :size="150" :currentIndex="currentIndexs" @change="navchange">
				</gui-switch-navigation>
			</view>
			<!-- style="flex:1;width: 100%;" -->
			<!-- :style="{height:mainHeight+'px', width:'750rpx'}" -->
			<swiper :current="currentIndexs" @change="swiperChange" :style="{height:mainHeight+'px', width:'750rpx'}">
				<!-- 轮播项目数量对应 上面的选项标签 -->
				<swiper-item @touchmove.stop="">
					<!-- 门诊详情 -->
					<outpatientService ref="outpatientService"  @report="report" @jyreport="jyreport" :info="mzInfo"></outpatientService>
				</swiper-item>
				<swiper-item @touchmove.stop="">
					<!-- 健康数据 -->
					<!-- <sugar></sugar> -->
					<healthy ref="healthy" :monitorId="monitorId" :info="jkInfo"></healthy>
				</swiper-item>
				<swiper-item @touchmove.stop="">
					<!-- 干预措施 -->
					<intervention ref="intervention"  :info="gyInfo"></intervention>
				</swiper-item>
				<swiper-item @touchmove.stop="">
					<!-- 疗效评估 -->
					<curativeEffect ref="curativeEffect"  :info="lxInfo"></curativeEffect>
				</swiper-item>
				<swiper-item @touchmove.stop="">
					<!-- 体制辨识 -->
					<constitution ref="constitution" @report="report" :info="tzInfo"></constitution>
				</swiper-item>
				<swiper-item @touchmove.stop="">
					<!-- 饮食日记 -->
					<timelineDiary ref="timelineDiary" :info="ysInfo"></timelineDiary>
					<!-- <timelineDiary ref="timelineDiary" :monitorId="monitorId" :info="ysInfo"></timelineDiary> -->
				</swiper-item>
			</swiper>


			<!-- 下达监测干预底部弹出 -->
			<gui-popup ref="gyguipopup" position="bottom" :zIndex='1'>
				<patientGy @gyclose="gyclose" :patientId="info.patientId" :taskTypeArr="taskTypeArr" @submitgy="submitgy"></patientGy>
			</gui-popup>
			<!-- 人群标签底部弹出 -->
			<gui-popup ref="guipopup" position="bottom" :zIndex='1'>
				<renqunFZ :genderArr="gender" :patientId="info.patientId" @rqclose="rqclose" @renqSubmit="renqSubmit"></renqunFZ>
			</gui-popup>
		</view>
	</gui-page>
</template>
<script>
	import { getIndicator,updateCrowdTag } from '@/api/patient.js'
	import timelineDiary from "./component/timelineDiary"
	import sugar from "./component/sugar.vue"
	import lego from "./component/lego.vue"
	import outpatientService from './component/outpatientService'
	import constitution from './component/constitution'
	import intervention from './component/intervention.vue'
	import curativeEffect from './component/curativeEffect.vue'
	import healthy from './component/healthy.vue'
	import patientGy from './component/patientGy.vue'
	import renqunFZ from './component/renqunFZ.vue'
	var graceJS = require('@/GraceUI5/js/grace.js');
	export default {
		components:{sugar,lego,outpatientService,constitution,intervention,curativeEffect,healthy,timelineDiary,patientGy,renqunFZ},
		data() {
			return {
				taskTypeArr:[],//下达干预-监测项目
				gender : [//人群分组-人群类型的数据
					{dictLabel:''}
				],
				// crowdType:[],
				// domainType:this.$common.domainType,
				// titleArray: ["一", "二", "三", "四", "五"],
				isRefresh:1,
				test: '医生在系统接诊后，患者才能绑定小程序，是因为程序设定了，患者与健康管理平台的信息绑定是因为程序设定了，患者与健康管理平台的信息绑定 ',
				// infokey: 2,
				currentIndexs: 0,
				// 全屏 loading
				pageLoading: true,
				// 选项卡标签
				tabs: [{
						id: 1,
						name: '诊疗记录'
					},
					{
						id: 3,
						name: '健康数据'
					},
					{
						id: 5,
						name: '干预措施'
					},
					{
						id: 6,
						name: '疗效评估'
					},
					{
						id: 4,
						name: '健康指导'
					},
					{
						id: 7,
						name: '饮食日记'
					},
				],
				// tabs : [{id:1,name:'门诊详情'},{id:2,name:'Two'}],
				// 选中选项的 索引
				currentIndex: 0,
				// 核心区域高度
				mainHeight: 200,
				// 类型（1、我是患者；3、我是团队患者）
				type: '',
				// 患者信息
				info: {},
				mzInfo: {},
				tzInfo: {},
				gyInfo: {},
				lxInfo: {},
				jkInfo: {},
				ysInfo: {},
				// 患者标签
				tags: [],
				// 舌面像
				LingualList: [],
				// 干预措施
				// getTaskData: [],
				// // 疗效评估
				// getEvaluationData: [],
				goafangUse: [],
				singleUnit: [],
				// 性别
				sexlits:[],
				monitorId:''//健康数据
			}
		},
		// onShow() {
		// 	this.isRefresh = this.isRefresh + 1;  //控制属性值改变触发子组件刷新
		// },
		onLoad: function(option) {
			// #ifdef H5
			//  $('body').on('touchmove', function (e) {
			//     e.preventDefault();
			// });
			// #endif
			if(uni.getStorageSync('mbmonitorId')){
				this.currentIndexs = 1
				this.monitorId = uni.getStorageSync('mbmonitorId');
				uni.removeStorageSync('mbmonitorId')
			}
      if (option.type != null) {
        this.infokey = option.type
        this.type = option.type
      }
			let patientId = uni.getStorageSync('mbdetailsPatientId');
			uni.removeStorageSync('mbdetailsPatientId')
			// 01. 获取页面主体高度
			graceJS.getRefs('guiPage', this, 0, (ref) => {
				ref.getDomSize('guiPageBody', (e) => {
					// console.log(e);
					// 02. 导航的高度
					// 可以 使用 graceJS.select() 函数动态获取， 也可以使用 uni.upx2px 转换已知高度
					// 轮播主体高度 = 页面高度 - 导航高度
					this.mainHeight = e.height - uni.upx2px(300);
					// this.pageLoading = false;
				});
			});
			this.$common.RequestData({
			  	url: `${this.$common.getDicts}sys_user_sex`,
			  	method: 'get',
			}, res => {
				this.sexlits = res.data
				this.getList(patientId)
			})
			// //openType:1是患者列表，2是首页的的健康数据，3是在线咨询的档案
			// if (option.openType == 2) {this.currentIndexs = 4;}
			// let patientInfo = uni.getStorageSync('mbpatientInfo');
			// this.$set(this,'info',patientInfo)
			// uni.removeStorageSync('mbpatientInfo')
			// console.log('详情页患者数据跳转==this.info',this.info)
			// this.getList(this.info.patientId)
		},
		onShow() {
			if(uni.getStorageSync('mbtasktype')){
				this.navchange(2)
				uni.removeStorageSync('mbtasktype')
			}
		},
		methods: {
			rqclose() {this.$refs.guipopup.close();},
			renqSubmit(e){//保存人群分组
				this.tags = [];
				this.getList(this.info.patientId)
				this.$refs.guipopup.close();
			},
			openRenq(){
				this.$common.RequestData({
					url: this.$common.getCrowdType + 'people_type',
					data: {},
					method: 'get',
				}, res => {
					if (res.code == 200) {
						this.gender = res.data
						this.$refs.guipopup.open();
					}else{
						this.$common.msg(res.msg)
					}
				})
			},
			showMenu(){
				this.$refs.gracePopupMenu.open();
			},
			hideMenu(){
				this.$refs.gracePopupMenu.close();
			},
			showFangan(){
				this.$common.navTo('/pages/patient/fanganTask?patientId=' + this.info.patientId);//好友组
			},
			tap(e){
				var id = e.currentTarget.dataset.id;
				if (id=='2') {//打开下达干预的底部弹窗
					getIndicator().then(res =>{
						this.taskTypeArr = [{id:0,indicators:'请选择',timingPointList:[]}].concat(res.data)
						// console.log('监测项目===',res)
					})
					this.$refs.gyguipopup.open();
					uni.setStorageSync("mbtasktype",e)
				} else{
					this.chooseTask(id)
				}
				this.$refs.gracePopupMenu.close();
			},
			chooseTask(e){
				this.$common.navTo('/pages/chat/task?patientId=' + this.info.patientId);//好友组
				uni.setStorageSync("mbtaskStatus",e)
				uni.setStorageSync("mbtasktype",e)
			},
			submitgy(e){
				this.getList(this.info.patientId)
				this.$refs.gyguipopup.close()
			},
			gyclose(e){//关闭下达干预的底部弹窗
				this.$refs.gyguipopup.close()
			},
			getList(e){
				// console.log('进详情界面的接口===',e)
				this.$common.RequestData({
					url: this.$common.patientList,
					data: {
						type:2,
						pageNum:1,
						pageSize:100,
						seachType:1,
						patientId:e
					},
					method: "get"
				}, res => {
					if (res.code==200) {
						this.info = res.rows[0]
						this.$set(this,'info',res.rows[0])
						this.pageLoading = false;
						this.navchange(this.currentIndexs)
						this.getDict()
						if(this.info.physical){
							this.physicalDict(this.info.physical);
						}
						if(this.info.crowdType){
							this.crowdTypeDict(this.info.crowdType)
						}
					}
				},true,fail=>{

				})
			},
			setGoaFangUse(value) {
				return this.goafangUse?.find(item => item.dictValue === value)?.dictLabel || ''
			},
			setGoaFangSingleUnit(value) {
				return this.singleUnit?.find(item => item.dictValue === value)?.dictLabel || ''
			},
			getDict() {
				// 获取膏方用法的字典
				this.$common.RequestData({
					url: `${this.$common.getDicts}cream_formula`,
					method: 'get',
				}, res => this.goafangUse = res.data)
				// 膏方单量单位的字典
				this.$common.RequestData({
					url: `${this.$common.getDicts}plaster_single_unit`,
					method: 'get',
				}, res => this.singleUnit = res.data)
			},
			changeTo: function(idx) {
				if (this.currentIndex == idx) {
					this.currentIndex = -1;
				} else {
					this.currentIndex = idx;
				}
			},
			init(){
				let index = this.currentIndexs
				let id = this.tabs[index].id
				if(id == 4 && this.info.patientId){
					// 体质辨识
					this.$set(this,'tzInfo',this.info)
					console.log('体质辨识患者==',this.tzInfo)
					this.$refs.constitution.tzcontent(this.info.patientId);
				}else if(id == 1 && this.info.patientId){
					// 门诊详情
					this.$set(this,'mzInfo',this.info)
					console.log('门诊详情mzInfo===',this.mzInfo)
					this.$refs.outpatientService.changInfo(2)
					// this.$refs.outpatientService.getMedicalList()
				}else if(id == 5 && this.info.patientId){
					// 干预措施
					this.$set(this,'gyInfo',this.info)
					this.$refs.intervention.getTaskList(this.info)
				}else if(id == 6 && this.info.patientId){
					// 疗效
					this.$set(this,'lxInfo',this.info)
					this.$refs.curativeEffect.getEvaluationList(this.info.patientId)
				}else if(id == 3 && this.info.patientId){
					// 健康数据
					console.log('id == 3进来健康数据')
					this.$set(this,'jkInfo',this.info)
					// this.$refs.healthy.
				}else if(id == 7 && this.info.patientId){
					this.$set(this,'ysInfo',this.info)
					this.$refs.timelineDiary.tzcontent(this.info.patientId)
				}
				this.pageLoading = false;
			},
			navchange: function(index,infoObj) {
				this.currentIndexs = index;
				this.init(infoObj?infoObj:this.info)
				if (index === 1) {
					this.isRefresh = this.isRefresh + 1; //控制属性值改变触发子组件刷新
				}
			},
			swiperChange: function(e) {
				var index = e.detail.current;
				this.currentIndexs = index;
			},
			// 舌面
			getgetLingualList(e) {
				// if (e) {
				// 	this.changInfo(e)
				// }
				let _this = this;
				this.$common.RequestData({
					url: this.$common.getLingualList,
					data: {
						patientId: this.info.patientId
					},
					method: 'get'
				}, res => {
					this.LingualList = res.rows
				})
			},
			// 性别字典
			sexDict(val) {
				if (val == 1) {
					return "男";
				} else if (val == 2) {
					return "女";
				} else {
					return "未知";
				}
			},
			// 体质字典
			physicalDict(val) {
				if (!val) return;
				// console.log(val);
				let arr = val.split(";");
				let dicts = [{
						id: 1,
						text: "平和质",
					},
					{
						id: 2,
						text: "气虚质",
					},
					{
						id: 3,
						text: "阳虚质",
					},
					{
						id: 4,
						text: "阴虚质",
					},
					{
						id: 5,
						text: "湿热质",
					},
					{
						id: 6,
						text: "气郁质",
					},
					{
						id: 7,
						text: "痰湿质",
					},
					{
						id: 8,
						text: "血瘀质",
					},
					{
						id: 9,
						text: "特禀质",
					}
				];
				if (arr) {
					arr.forEach(item => {
						dicts.forEach(items => {
							if (item == items.id) {
								this.tags.push(items.text);
								return false;
							}
						})
					})
				}
				// console.log(this.tags);
			},
			crowdTypeDict(val) {
				if (!val) return;
				var list = this.info.crowdLabel.split(';')
				this.$common.RequestData({
					url: this.$common.queryList,
					data:{
						crowType:val
					},
					method: 'get',
				},res=>{
					if(res.data.length >0){
						var obj = ''
						list.forEach(item => {
							res.data.forEach(items => {
								if (item == items.id) {
									obj += items.title+';'
								}
							})
						})
						this.tags.push(this.info.crowdName?(this.info.crowdName+';'+obj):obj);
					}
				})
			},
			propertiesDict(val) {
				if (!val) return;
				let arr = val.split(";");
				let dicts = [{
						id: 0,
						text: "老人",
						checked: false
					},
					{
						id: 1,
						text: "小孩",
						checked: false
					},
					{
						id: 2,
						text: "父女",
						checked: false
					}
				];
				if (arr) {
					arr.forEach(item => {
						dicts.forEach(items => {
							if (item == items.id) {
								this.tags.push(items.text);
								return false;
							}
						})
					})
				}
			},
			manageLevelDict(val) {
				if (!val) return;
				let arr = val.split(";");
				let dicts = [{
						id: 0,
						text: "普通",
						checked: false
					},
					{
						id: 1,
						text: "特殊",
						checked: false
					},
					{
						id: 2,
						text: "加强",
						checked: false
					}
				];
				if (arr) {
					arr.forEach(item => {
						dicts.forEach(items => {
							if (item == items.id) {
								this.tags.push(items.text);
								return false;
							}
						})
					})
				}
			},
			dateFormat(val) {
				return this.$common.parseTime(val, '{y}-{m}-{d}')
			},
			//检查报告
			report(items) {
				this.$common.navTo('/pages/patient/report?info=' + JSON.stringify(items))

			},
			//舌面记录
			tongue(item) {
				this.$common.navTo('/pages/patient/tongue?info=' + JSON.stringify(item))
			},
			//检验报告
			jyreport(items) {
				this.$common.navTo('/pages/patient/jyreport?info=' + JSON.stringify(items))

			},
		},
	}
</script>
<style lang="scss" scoped>
	.gui-rows-title{
		flex:1
	}
	.visitDate-row{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.navscroll{
		white-space: nowrap;
		// height: 100rpx;
		::v-deep ::-webkit-scrollbar {
			width: 4px !important;
			height: 1px !important;
			overflow: auto !important;
			background: transparent !important;
			-webkit-appearance: auto !important;
			display: block;
		}
		.garce-items{
			display: inline-block; // 设置为行内块
		}
	}
	.prescription{
		display: flex;
		flex-direction: row;
		flex-flow:row wrap;
	}
	.prescription-item{
		width: 30%;
		line-height: 26px;
		text-align: center;
	}
	>>>.gui-scroll-x-items {
		align-items: center;
	}

	.demo-nav {
		padding: 15rpx 30rpx;
	}

	.demo-text {
		line-height: 200rpx;
		padding-bottom: 3000px;
	}

	.grace-box-banner .garce-items .line2 {
		font-size: 28rpx;
		color: #008AFF;
		display: inline-block; // 设置为行内块
		border: 1px solid #62dbff;
		padding: 0 15rpx;
		margin: 0 10rpx;
		border-radius: 20rpx;
	}

	.line2.active {
		color: #ffffff !important;
		/* font-weight:bold; */
		background-color: #008AFF;
	}

	.l-timeline-l {
		border-left: 1px solid #aaaaaa;
	}

	.l-timeline-b {
		/* border-bottom: 2px solid #008AFF; */
	}

	.l-time {
		position: relative;
		top: -15rpx;
	}

	.acolor {
		background-color: #7784eb;

	}

	.bcolor {
		background-color: #7784eb;
		height: 40rpx;
		line-height: 40rpx;
		width: 190rpx;
	}

	.tagicon {
		margin-right: 10rpx;
		height: 40rpx;
		width: 6rpx;
		border-radius: 5rpx;
		background: #008AFF;
		display: block;
	}

	.l-icon {
		background: #008AFF;
		width: 25rpx;
		height: 25rpx;
		border-radius: 25rpx;
		position: relative;
		top: -50rpx;
		left: -15rpx;
	}

	.l-content {
		position: relative;
		top: -25rpx;
	}

	.gui-accordion-icon {
		width: 50rpx;
		// height: 80rpx;
		// line-height: 80rpx;
		font-size: 32rpx;
	}

	.gui-flex-direction-row {
		flex-direction: row-reverse;
	}

	.gui-accordion-title-text {
		width: 200rpx;
		flex: 1;
	}

	>>>.gui-block-text {
		font-size: 30rpx !important;
	}

	.resuimg {
		width: 24px;
		height: 24px;
		font-size: 24px;
		line-height: 24px;
		vertical-align: middle;
		color: rgba(0, 186, 173, 1);
	}

	.resutext {
		width: 70px;
		height: 21px;
		font-size: 14px;
		text-align: left;
		font-weight: bold;
		line-height: 24px;
		padding-left: 8rpx;
		color: rgba(80, 80, 80, 1);
	}

	.propose_net {
		width: 100%;
		height: auto;
		margin-top: 5rpx;
		padding: 0 10rpx;
		text-indent: 2em;
		line-height: 52rpx;
		overflow-x: scroll;
	}

	.end_text {
		width: 325px;
		height: 42px;
		font-size: 14px;
		text-align: center;
		margin: 20% auto 0;
		color: rgba(212, 48, 48, 1);
	}

	.propose {
		// width: 92%;

	}


	/* 干预 */
	.ganyuFu {
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.ios {
		font-size: 35rpx;
		margin: 10rpx 10rpx;
		color: #1aca0d;
	}

	/* 疗效评估 */
	.laioxiaoFu {
		/* display: flex; */
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.laioxiaoZi {
		display: flex;
		align-items: center;
		line-height: 50rpx;
		padding: 10rpx 0rpx 10rpx 15rpx;
	}

	.Aios {
		font-size: 50rpx;
		margin: 10rpx 20rpx 10rpx 10rpx;
		color: #1aca0d;
		/* height: 100% !important; */
		display: flex;
		align-items: center;
		float: left;
	}

	.yuandian {
		float: left;
		display: block;
		line-height: 50rpx;
		top: 10px;
		width: 8px;
		height: 8px;
		border-radius: 20px;
		/* background: #cbd0db; */
		background: #6f6f6f;
		margin-top: 8px !important;
		margin: 8px;

	}

	/* 舌面详情按钮 */
	.sm_xq {
		float: right;
		margin-right: 30rpx;
	}

	/* 头像 */
	.head_img {
		border-radius: 10rpx;
		border: 1px solid #cdcdcd;
	}

	>>>.gui-list-title-text {
		font-size: 28rpx !important;
	}
</style>
