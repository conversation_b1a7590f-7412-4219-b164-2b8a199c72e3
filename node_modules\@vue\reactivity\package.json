{"_from": "@vue/reactivity@3.4.27", "_id": "@vue/reactivity@3.4.27", "_inBundle": false, "_integrity": "sha512-kK0g4NknW6JX2yySLpsm2jlunZJl2/RJGZ0H9ddHdfBVHcNzxmQ0sS0b09ipmBoQpY8JM2KmUw+a6sO8Zo+zIA==", "_location": "/@vue/reactivity", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/reactivity@3.4.27", "name": "@vue/reactivity", "escapedName": "@vue%2freactivity", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/", "/@vue/runtime-core"], "_resolved": "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.4.27.tgz", "_shasum": "6ece72331bf719953f5eaa95ec60b2b8d49e3791", "_spec": "@vue/reactivity@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueReactivity", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/shared": "3.4.27"}, "deprecated": false, "description": "@vue/reactivity", "exports": {".": {"types": "./dist/reactivity.d.ts", "node": {"production": "./dist/reactivity.cjs.prod.js", "development": "./dist/reactivity.cjs.js", "default": "./index.js"}, "module": "./dist/reactivity.esm-bundler.js", "import": "./dist/reactivity.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/reactivity#readme", "jsdelivr": "dist/reactivity.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/reactivity.esm-bundler.js", "name": "@vue/reactivity", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity"}, "sideEffects": false, "types": "dist/reactivity.d.ts", "unpkg": "dist/reactivity.global.js", "version": "3.4.27"}