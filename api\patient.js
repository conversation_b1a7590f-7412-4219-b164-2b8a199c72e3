import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 患者列表- 保存建档
export function addArchival(data){
	return Http.post('/zwb/patient/add/archival',data)
}
// 患者列表- his获取用户信息
export function getAppointPatientlist(data){
	return Http.get('/zwb/patient/getAppointPatientlist',data)
}
// 患者列表- 舌面详情
export function getPerform(data){
	return Http.get('/doctor/perform/'+data)
}
// 患者列表- 新增舌面
export function addTaskPerform(data){
	return Http.post('/doctor/perform/addTaskPerform',data)
}
// 患者列表- 编辑舌面
export function editTaskPerform(data){
	return Http.post('/doctor/perform/editTaskPerform',data)
}
// 患者列表- 删除舌面
export function delTaskPerform(data){
	return Http.get('/doctor/perform/delTaskPerform/'+data)
}

// 患者列表- 创建档案时，获取应用下医生及护士列表
export function getDutyUserList(data){
	return Http.get('/doctor/user/getDutyUserList',data)
}
// 患者详情- 查询人群标签列表--慢病项目
export function patientCrowdLabel(data){
	return Http.get('/doctor/patient/crowdLabel/'+data)
}

// 患者详情- 查询人群标签列表
export function crowdLabelQueryList(data){
	return Http.get('/zwb/crowdLabel/queryList',data)
}
// 患者详情- 更新患者人群标签信息
export function updateCrowdTag(data){
	return Http.post('/doctor/patient/update/crowdTag',data)
}

// 根据参数键名查询参数值
export function getConfigKey(key) {
	return Http.get('/system/config/configKey/' + key)
}

// 患者详情- 下达干预-查询监测项目
export function getIndicator(data) {
	return Http.get('/doctor/task/monitor/indicator',data)
}

// 患者详情- 下达干预-保存下达监测
export function monitorSet(data) {
	return Http.post('/doctor/task/monitor/set',data)
}

// 患者详情- 下达干预-保存下达问卷、宣教
export function setTask(data) {
	return Http.post('/doctor/task',data)
}


// 患者详情-下达方案-查询干预方案类型
export function programType(data){
	return Http.get('/doctor/task/program/type',data)
}

// 患者详情-下达方案-查询干预方案内容
export function programConten(data){
	return Http.get('/doctor/task/program/conten',data)
}

// 患者详情-下达方案-确定发送内容
export function programIntervene(data){
	return Http.post('/doctor/task/program/intervene',data)
}

// 患者详情-下达方案-确定发送内容
export function interveneInspect(patientId,programmeTypeId){
	return Http.get('/doctor/task/program/intervene/inspect/'+patientId+'/'+programmeTypeId)
}

// 患者详情-健康指导-中医禁食疗法
export function listFastingTherapy(data){
	return Http.get('/zwb/fastingTherapy/list',data)
}

// 患者详情-营养干预方案
export function listNutritionIntervention(data){
	return Http.get('/zwb/nutritionIntervention/list',data)
}

// 患者详情-营养干预方案PDF预览
export function previewNutritionInterventionPDF(data){
	return Http.get('/zwb/nutritionIntervention/previewPDF', data, {}, {
		responseType: 'blob'
	})
}