<template>
  <view style="height: 100vh; background-color: #f5f6fe;">
    <view class="pl-50 l-loginbg">
      <view class="gui-flex gui-align-items-center " style=" height: 70%; ">
        <view style="font-size: 64rpx;"><text class="gui-color-white gui-bold">门诊慢病</text>
          <view><text style="color: #0ADBF4;">健康管理</text></view>
        </view>
      </view>
      <view class="gui-color-white gui-bold health"> HEALTH</view>
    </view>
    <view class="mt-40"></view>
    <view class="mx-40" style="background-color:#f5f6fe;">
      <form class="mt-60"  @submit="submit">
        <view class="gui-list  gui-padding":style="showDropdown?'height: 400rpx;':''" >
          <view class="gui-list-items gui-border-b" style="position: relative;">
            <img class="iconwh" src="../../static/images/account.png" />
            <view class="gui-list-body ">
              <view class="gui-list-title">
                <input @input="inputUser" type="text" class="gui-form-input fs-36" name="username" placeholder="账号"
                       v-model="form.username" />
				<text class="gui-icons fs-40 pr-20" v-if="showClearIcon || form.username" @click="clearIcon">&#xe78a;</text>
				<text v-if="userOptions.length>0" @click="toggleDropdown" class=" gui-icons fs-50 px-10 gui-bold">{{showDropdown ?'&#xe654;':'&#xe603;'}}</text>
              </view>
            </view>
			<!-- 下拉框 -->
			<view  class="dropdown" v-if="showDropdown" >
			  <scroll-view  scroll-y  class="dropdown-list" >
			    <view class="dropdown-item fs-36" :style="form.username==item.createBy?'color: #438dff;':''"
					v-for="(item, index) in userOptions" :key="index" @click="selectItem(item)" >
			      <view class="gui-flex gui-space-between">
			      	<view class="">{{ item.createBy }}</view>
					<text v-if="form.username==item.createBy" class=" gui-icons fs-36">&#xe60f;</text>
			      </view>
			    </view>
			  </scroll-view>
			</view>
          </view>
		  
		  
          <view class="" v-if="!selectUser">
          	<view class="gui-list-items gui-border-b">
          	  <img class="tc iconwh" src="../../static/images/password.png" />
          	  <view class="gui-list-body ">
          	    <view class="gui-list-title ">
          	      <input :type="passwordType" class="gui-form-input fs-36"
          	             style="width: 100%;border:none;outline: none;display:block;margin-left: -6rpx;"
          	             name="password" placeholder="密码" v-model="form.password" />
          	      <image :src="imgurl" mode="" @click="changeStatus" class="imageshow"></image>
          	    </view>
          	  </view>
          	</view>
          	<view class="gui-list-items gui-border-b" v-if="captchaEnabled">
          	  <text class="gui-icons gui-bold fs-40  pr-5" style="color: #438dff;">&#xe687;</text>
          	  <view class="gui-list-body gui-flex">
          	    <view class="gui-list-title">
          	      <input maxlength="8" type="text" class="gui-form-input fs-36" name="code" placeholder="验证码"
          	             v-model="form.code" />
          	    </view>
          				  <view class="ml-40">
          				  	<image @click="getCode" :src="codeUrl" mode="" style="height: 90rpx; width: 300rpx;"></image>
          				  </view>
          	  </view>
          	</view>
          </view>
        </view>
        <view class="gui-flex mt-30 ml-30 " v-if="!selectUser">
          <checkbox-group name="rpassword" class="gui-flex gui-rows gui-wrap" @change="chanpassword">
            <label class="gui-check-item">
              <checkbox color="#008AFF" :value="form.password" :checked="checkedtype"></checkbox>
              <text class="gui-text gui-primary-color ml-20 fs-36">记住密码</text>
            </label>
          </checkbox-group>
        </view>
        <view style="display: flex;justify-content: center;" class="mt-50" v-show="bntShow">
          <button :loading="submitLoading" class="gui-button" formType="submit" v-if="!selectUser">
            <text class="gui-color-white ">登 录</text>
          </button>
		  <button :loading="submitLoading" class="gui-button" @click="toSubmit" v-if="selectUser">
		    <text class="gui-color-white ">安全登录</text>
		  </button>
        </view>
      </form>
    </view>
    <!--    授权弹窗-->
    <gui-popup ref="guipopup1" :canCloseByShade="false">
      <view class="gui-bg-white" style="padding:50rpx;">
        <view>
          <form>
            <view class="gui-border-b">
              <input type="text" class="gui-form-input"
                     v-model="loginCheckCode" placeholder="请输入授权码" />
            </view>
            <view class="gui-color-red">
              {{errMsg}}
            </view>
            <view style="margin-top:38rpx;text-align: center">
              <button class="gui-button" style="width: 100%" @click="doLoginCheckCode">
                <text class="gui-color-white ">确 定</text>
              </button>
            </view>
          </form>
        </view>
      </view>
    </gui-popup>
  </view>

</template>
<script>
	import Cookies from "js-cookie";
	import {encrypt, decrypt} from '@/tools/utils/jsencrypt.js'
	import Pako from "pako";
	import {getCodeImg,getUserListByCode,loginByWeCom} from '@/api/login.js'
var graceChecker = require("@/GraceUI5/js/checker.js");
export default {
  data() {
    return {
		showClearIcon:false,//账户输入框的清楚图标
		submitLoading:false,//登录按钮等待
		bntShow:true,//登录显示
      form: {
		  username:'',
		  password:'',
		  code: "",
		  uuid: ""
	  },
      uuid: '',
      checkedtype: true,
      passwordType: 'password',
      imgurl: '../../static/images/shut.png',
      platform: '',
      loginCheckCode: '',
      errMsg: '',
	  wxOpenId:'',
	  codeUrl: "",//验证码图片
	  captchaEnabled:false,
	  showDropdown:false,//历史账户选择窗口
	  selectUser:false,//为真时显示安全登录按钮
	  userOptions:[],//历史账户列表
	  createBy:'',//安全登录的参数--用于判断普通登录还是安全登录
	  sysUid:'',//安全登录的参数
	  wxUser:'',//安全登录的参数
	  wxcode:'',//安全登录的参数
    }
  },
  onShow() {
    if (uni.getStorageSync('mbrememberpassword') && uni.getStorageSync('mbusername')) {
      this.form.username = uni.getStorageSync('mbusername');
      this.form.password = uni.getStorageSync('mbrememberpassword')
    }else{
      this.form.password = ''
      this.form.username = uni.getStorageSync('mbusername')
    }
	this.getCode();
    // this.getuuid();
    this.checkPlatform()
  },
  onLoad() {
  	this.getCookie();
	// #ifdef H5
	// 获取user-agaent标识头
	let ua = window.navigator.userAgent.toLowerCase();
	//判断 微信浏览器 或者 企业微信浏览器
	if (this.isWechat() || this.isWorkWechat()) {
		this.bntShow = false;
		// TODO 医生端授权开关
		// alert('医生端授权开关'+this.$common.bingingWxUser)
		if (this.$common.bingingWxUser) {
			let params = this.getRequestParams();
			let code = params['code'];
			this.wxcode = code
			this.toWxLogin()
		}
	}
	// #endif
  },
  methods: {
	clearIcon() {
	    this.form = {
			username:null,
			password:null,
		};
	    this.showClearIcon = false;
		this.selectUser = false;
		this.bntShow = true;
	},
	  inputUser(e){
		  if (e.detail.value != this.createBy) {
		  	this.selectUser = false;
			this.showDropdown = false;
		  }
		  if (e.detail.value.length > 0) {
		  	this.showClearIcon = true;
		  } else {
		  	this.showClearIcon = false;
		  }
	  },
	  toggleDropdown() {//选择历史账户图标
	    this.showDropdown = !this.showDropdown
	  },
	selectItem(item) {//选择历史账户
	  this.form.username = item.createBy
	  this.createBy = item.createBy
	  this.sysUid = item.sysUid
	  this.wxUser = item.wxUser
	  this.showDropdown = false;
	  this.selectUser = true;
	  this.showClearIcon = true;
	},
	toSubmit(){//历史账户登录
		if (this.submitLoading) {return}
		this.submitLoading = true;
		let that = this
		loginByWeCom({
			wxcode:this.wxcode,
			sysUid:this.sysUid,
			wxUser:this.wxUser
		}).then(res =>{
			uni.setStorageSync('mbwxUserId', that.wxUser)
			if (res.data.secret) {
				uni.showToast({
				  title: '登录成功!',
				  duration: 1500
				})
				setTimeout(function() {
					uni.setStorageSync("mbtoken", res.data.token);
				  uni.setStorageSync("mbusername", that.form.username);
				  uni.setStorageSync("mbsecret", res.data.secret);
				  that.$common.navLaunch('/pages/home/<USER>')
				  uni.setStorageSync("logintype", 'login');
				  that.submitLoading = false;
					uni.removeStorageSync("outMy")
				}, 1500)
			}else{
				that.submitLoading = false;
				that.$common.msg(res.data.msg)
			}
			
		})
	},
	getCookie() {//账号、密码加密
	    const username = Cookies.get("username");
	    const password = Cookies.get("password");
	    const checkedtype = Cookies.get('checkedtype')
	    this.form = {
	      username: username === undefined ? this.form.username : username,
	      password: password === undefined ? this.form.password : decrypt(password),
	      checkedtype: checkedtype === undefined ? false : Boolean(checkedtype)
	    };
	  },
    //授权码确定
    doLoginCheckCode(){
      this.errMsg = "";
      let that = this;
      this.$common.RequestDataNo({
        url: this.$common.doLoginCheckCode,
        data: {
          type: "2",
          code: this.loginCheckCode
        },
        method: "get"
      }, function(res) {
        if(res.code == 200 ){
          that.$refs.guipopup1.close(); //关闭授权码弹窗
        }else {
          that.errMsg = res.msg
        }
      })
    },
    //根据环境请求后期看是否需要授权验证码
    checkPlatform() {
      const ua = navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        this.platform = 'weixin'; // 微信
      } else if (ua.match(/wxwork/i) == "wxwork") {
        this.platform = 'wxwork'; // 企业微信
      } else {
        this.platform = 'unknown'; // 无法确定当前应用在哪个平台上打开
        let loginCheckUrl = location.host;
        //后台请求配置看是否要授权
        let that = this;
        this.$common.RequestDataNo({
          url: this.$common.getLoginCheckCode,
          data: {
            type: "2",//1 pc管理端 2移动医护h5
            loginCheckUrl: loginCheckUrl
          },
          method: "post"
        }, function(res) {
          if(res.data.loginCheck){
            that.$refs.guipopup1.open(); //打开授权码弹窗
          }
        })
      }
    },
    // 切换
    changeStatus() {
      this.passwordType = this.passwordType === 'password' ? 'text' : 'password';
      this.imgurl = this.imgurl == require('../../static/images/open.png') ? require(
          '../../static/images/shut.png') : require('../../static/images/open.png');
    },
    // 记住密码
    chanpassword(e) {
      if (e.detail.value.length > 0) {
        this.$set(this, "checkedtype", true)
      } else {
        this.$set(this, "checkedtype", false)
        this.rememberpassword = null
      }
    },
    // getuuid() {
    //   this.$common.RequestDataNo({
    //     url: this.$common.captchaImage,
    //     data: {},
    //     method: "get"
    //   }, res => {
    //     this.uuid = res.data.uuid;
    //   })
    // },
	getCode() {
		this.form.code = ''
	  getCodeImg().then(res => {
	    this.captchaEnabled = res.data.enabled;
	    if(res.data.enabled){
	      this.codeUrl = "data:image/gif;base64," + res.data.img;
	      this.form.uuid = res.data.uuid;
		  this.$forceUpdate()
	    }
	  });
	},
    submit(e) {
		if (!this.form.username) {return this.$common.msg('请输入账号！')}
		if (!this.form.password) {return this.$common.msg('请输入密码！')}
		if (!this.form.code) {return this.$common.msg('请输入验证码！')}
		// let wxOpenId = uni.getStorageSync('mbwxOpenId') || '';
		if (!this.form.username && !this.form.password) {return}
      let that = this;
      var rule = [{
        name: "username",
        checkType: "string",
        checkRule: "1,20",
        errorMsg: "请输入账号"
      }, {
        name: "password",
        checkType: "string",
        checkRule: "1,20",
        errorMsg: "请输入密码"
      }];
	  var formData = this.form;
	  var checkRes = graceChecker.check(formData, rule);



	  if (this.checkedtype) {
	    Cookies.set("username", formData.username, { expires: 30 });
	    Cookies.set("password", encrypt(formData.password), { expires: 30 });
	    Cookies.set('checkedtype', this.checkedtype, { expires: 30 });
	  } else {
	    Cookies.remove("username");
	    Cookies.remove("password");
	    Cookies.remove('checkedtype');
	  }


	  //账号、密码加密
	  const loninData = {...formData};
	  let username1 = encrypt(formData.username);
	  let password1 = encrypt(formData.password);
      //第二步：加密后的账号、密码压缩传输
      loninData.username = Pako.deflate(username1).toString();
      loninData.password = Pako.deflate(password1).toString();
		if (this.submitLoading) {return}
		this.submitLoading = true;
        this.$common.RequestDataNo({
          url: this.$common.login,
          data: loninData,
          method: "post"
        }, function(res) {
          if (that.checkedtype) {
            that.rememberpassword = that.form.password;
            uni.setStorageSync("mbrememberpassword", that.rememberpassword)
          }else{
            uni.removeStorageSync( "mbrememberpassword")  // 防止二次登录取消记住密码
          }
		  console.log('login res', res)
          if (res.code == 200) {
				uni.setStorageSync("mbtoken", res.data.token);
			  if (res.data.pwdStatus) {
				  setTimeout(function() {
					  uni.showModal({
					  	title: '提示',
					  	content: res.data.msg,
						showCancel:false,
					  	success: function (res) {
					  		if (res.confirm) {
					  			that.$common.navLaunch('/pages/my/revise')
					  		} else if (res.cancel) {
					  			that.getCode();
					  		}
					  	}
					  });

				  }, 10)
				return
			  }else{
				  uni.showToast({
				    title: '登录成功!',
				    duration: 1500
				  })
				  setTimeout(function() {
				    uni.setStorageSync("mbusername", formData.username);
				    uni.setStorageSync("mbsecret", res.data.secret);
				    that.$common.navLaunch('/pages/home/<USER>')
					that.submitLoading = false;
				  }, 1500)
			  }
          } else {
			  that.getCode();
			  that.submitLoading = false;
            // this.getuuid();
          }
        })
    },
	
	toWxLogin() {
		// 微信登录逻辑
		// alert('进来了微信登录逻辑'+this.wxcode)
		// uni.showLoading({
		// 	title:"授权加载中...",
		// });
		let lastWxcode =  uni.getStorageSync('last-wxcode')
		if (!this.wxcode || lastWxcode == this.wxcode) {
			let redirect_uri = window.location.href
			console.log('redirect_uri', redirect_uri)
			// 1000066--慢病正式环境
			// 1000018--公司测试环境
			let uri =
				'https://open.weixin.qq.com/connect/oauth2/authorize?appid='+this.$common.appid+'&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid='+this.$common.agentid+'#wechat_redirect'
			window.location.href = uri.replace("REDIRECT_URI", redirect_uri)
		} else {
			uni.setStorageSync('last-wxcode',this.wxcode)
			// this.toBinding()
			this.toGetUserListByCode();//账户列表
		}
	},
	getRequestParams() {
		let url = location.href;
		let requestParams = {};
		if (url.indexOf('?') !== -1) {
			let str = url.substr(url.indexOf('?') + 1); //截取?后面的内容作为字符串
			console.log(str, '?后面的内容');
			let strs = str.split('&'); //将字符串内容以&分隔为一个数组
			console.log(strs, '以&切割的数组');
			for (let i = 0; i < strs.length; i++) {
				requestParams[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);
				// 将数组元素中'='左边的内容作为对象的属性名，'='右边的内容作为对象对应属性的属性值
			}
		}
		return requestParams;
	},
	toGetUserListByCode() {
		if (!this.wxcode) {return this.toWxLogin()}
		uni.showLoading({
			title:"数据加载中...",
		});
		getUserListByCode({code:this.wxcode}).then(res => {
			uni.hideLoading();
		  if (res.data.length>0) {
			this.userOptions = res.data
		  	this.form.username = res.data[0].createBy
		  	this.createBy = res.data[0].createBy
		  	this.sysUid = res.data[0].sysUid
		  	this.wxUser = res.data[0].wxUser
		  	this.showDropdown = false;
		  	this.selectUser = true;
			this.showClearIcon = true;
			if (!uni.getStorageSync('outMy')) {//首次进入的时候需要自动登录，在个人中心点击退出登录的时候不能自动登录
				this.toSubmit();
			}
		  }
		  this.bntShow = true;
		}).catch(err => {this.bntShow = true;})
	},
	toBinding() {
		// 向后台发起请求 携带code
		let queryParams = {
			code: this.wxcode
		}
		this.$common.RequestData({
			url: this.$common.toBinding,
			data: queryParams,
			method: "get"
		}, res => {
			uni.setStorageSync('mbwxUserId',rs.data.wxUser)
			this.wxcode = ''
		}, null, err => {
			console.log(err);
		})
	},
	isWechat() {
		//获取user-agaent标识头
		var ua = window.navigator.userAgent.toLowerCase();
		//判断ua和微信浏览器的标识头是否匹配
		if (ua.match(/micromessenger/i) == 'micromessenger') {
			return true;
		} else {
			return false;
		}
	},
	isWorkWechat() {
		//获取user-agaent标识头
		var ua = window.navigator.userAgent.toLowerCase();
		//判断ua和微信浏览器的标识头是否匹配
		if ((ua.match(/micromessenger/i) == 'micromessenger') && (ua.match(/wxwork/i) == 'wxwork')) {
			return true;
		} else {
			return false;
		}
	},
  }
}
</script>
<style scoped>
.l-loginbg {
  height: 43%;
  background-color: #7585EA;
  background-image: url(../../static/images/pt.png);
  background-size: 75%;
  border-bottom-left-radius: 50rpx;
  border-bottom-right-radius: 50rpx;
  background-repeat: no-repeat;
  background-position: 139% 13%;
}

.gui-border-b {
  border-bottom: 2rpx solid #E5E5E5;
}

.gui-form-label {
  box-shadow: 6rpx 10px 5px #dddcfe;
  width: 90rpx;
  height: 90rpx;
  font-size: 28rpx;
  line-height: 90rpx;
  overflow: hidden;
  background-color: #76bdef;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 标题 */

.gui-form-item {
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}

/* 表单项目 */

.gui-button {
  width: 327px;
  height: 50px;
  background: #7585EA;
  border-radius: 6px 6px 6px 6px;
  opacity: 1;
  filter: blur(undefinedpx);
}

.gui-button-text {
  line-height: 80rpx;
}

.health {
  font-size: 120rpx;
  color: white;
  background: linear-gradient(#9fabee, #7282E5 90%, transparent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
}

.iconwh {
  width: 40rpx
}

.gui-list-title input {
  width: 100%;
}

.imageshow {
  display: block;
  width: 50rpx;
  height: 50rpx;
  max-width: 50rpx;
  max-height: 50rpx;
  margin: 10rpx 10rpx;
}

.dropdown {
	background-color: #7282E5;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 400rpx;
  margin-top: 8rpx;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.dropdown-list {
  max-height: 200px;
}

.dropdown-item {
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #eee;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

input::-ms-reveal {
  display: none;
}
</style>
