{"_from": "@vue/compiler-sfc@3.4.27", "_id": "@vue/compiler-sfc@3.4.27", "_inBundle": false, "_integrity": "sha512-nDwntUEADssW8e0rrmE0+OrONwmRlegDA1pD6QhVeXxjIytV03yDqTey9SBDiALsvAd5U4ZrEKbMyVXhX6mCGA==", "_location": "/@vue/compiler-sfc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-sfc@3.4.27", "name": "@vue/compiler-sfc", "escapedName": "@vue%2fcompiler-sfc", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.4.27.tgz", "_shasum": "399cac1b75c6737bf5440dc9cf3c385bb2959701", "_spec": "@vue/compiler-sfc@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.24.4", "@vue/compiler-core": "3.4.27", "@vue/compiler-dom": "3.4.27", "@vue/compiler-ssr": "3.4.27", "@vue/shared": "3.4.27", "estree-walker": "^2.0.2", "magic-string": "^0.30.10", "postcss": "^8.4.38", "source-map-js": "^1.2.0"}, "deprecated": false, "description": "@vue/compiler-sfc", "devDependencies": {"@babel/types": "^7.24.0", "@vue/consolidate": "^1.0.0", "hash-sum": "^2.0.0", "lru-cache": "10.1.0", "merge-source-map": "^1.1.0", "minimatch": "^9.0.4", "postcss-modules": "^6.0.0", "postcss-selector-parser": "^6.0.16", "pug": "^3.0.2", "sass": "^1.76.0"}, "exports": {".": {"types": "./dist/compiler-sfc.d.ts", "node": "./dist/compiler-sfc.cjs.js", "module": "./dist/compiler-sfc.esm-browser.js", "import": "./dist/compiler-sfc.esm-browser.js", "require": "./dist/compiler-sfc.cjs.js"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "name": "@vue/compiler-sfc", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "types": "dist/compiler-sfc.d.ts", "version": "3.4.27"}