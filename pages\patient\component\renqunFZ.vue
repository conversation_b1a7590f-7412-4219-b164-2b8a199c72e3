<template>
	<view class="gui-relative gui-box-shadow gui-bg-white">
		<view class="" style="height: 70vh;">
			<view class="px-40 pt-20">
				<view class="w-100 gui-flex gui-align-items-center gui-justify-content-center gui-bold fs-36">
					设置人群分组
				</view>
				<view class="gui-form-item gui-border-b">
					<text class="gui-form-label fs-32" style="width: 160rpx;">人群类型：</text>
					<view class="gui-form-body">
						<picker mode="selector" :range="gender" @change="pickerChange" range-key="dictLabel">
							<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
								<text class="gui-text fs-32">{{gender[genderIndex].dictLabel}}</text>
								<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
							</view>
						</picker>
					</view>
				</view>
				<view class="pt-20">
					<view class="font-bold">
						<text class="gui-text fs-32">{{gender[genderIndex].dictLabel}} 标签选择：</text>
					</view>
					<scroll-view scroll-y="true" style="height: 42vh;" show-scrollbar="true">
						<view style="content: ''; overflow: hidden;"></view>
						<view style="margin-top:15rpx;">
							<gui-stags checkedBg="gui-bg-zdy" :tags="leixs" :padding="30" :size="32" :lineHeight="2.5"
								@change="crowdTypeChange" type="checkbox"></gui-stags>
						</view>
					</scroll-view>

				</view>
			</view>
			<view style="position: absolute; " class="w-100 gui-flex gui-space-between fs-36" :style="openType==2?'bottom: 140rpx;':'bottom: 0rpx;'">
				<view @tap.stop="rqclose"
					class="w-50 gui-flex gui-align-items-center gui-justify-content-center py-30 gui-bg-gray">取消</view>
				<view @click="submitRenq"
					class="w-50 gui-flex gui-align-items-center gui-justify-content-center zhuti-bnt-bg gui-color-white py-30">
					保存设置</view>
			</view>
		</view>
		<gui-iphone-bottom></gui-iphone-bottom>
	</view>
</template>
<script>
	import { patientCrowdLabel,updateCrowdTag } from '@/api/patient.js'
	export default {
		props: {
			genderArr: {
				type: [Object, Array],
				default () {
					return []
				}
			},
			patientId: {
				type: String,
				default: ''
			},
			openType: {
				type: Number,
				default: 1
			}
		},
		watch:{
			genderArr:{
				handler(newValue,oldValue){
					if (newValue) {
						this.gender = newValue
						this.getCrowdLabel(this.gender[this.genderIndex].id)
					}
				},
				deep:true,
				immediate:true
			}
		},
		data() {
			return {
				gender: [{
					dictLabel: ''
				}],
				genderIndex: 0,
				leixs: [{
						id: 1,
						text: "标签1",
						checked: false
					},
					{
						id: 1,
						text: "标签2",
						checked: false
					},
					{
						id: 1,
						text: "标签3",
						checked: false
					},
					{
						id: 1,
						text: "标签4",
						checked: false
					},
					{
						id: 1,
						text: "标签5",
						checked: false
					}
				],
				crowdType: [],
				
			}
		},
		computed: {

		},
		methods: {
			// 人群类型选择
			pickerChange: function(e) {
				this.genderIndex = e.detail.value;
				this.getCrowdLabel(this.gender[this.genderIndex].id)
			},
			// 选择人群标签
			crowdTypeChange: function(e,data) {
				this.crowdType = []
				e.map(item=>{
					this.crowdType.push(data[item].id);
				})
			},
			//获取人群标签
			getCrowdLabel(id) {
				if (id) {
					uni.showLoading({
						title: '请求中'
					});
					patientCrowdLabel(id).then(res => {
						this.leixs = this.arr(res.data)
						// this.leixs = res.data
					})
				}
			},
			arr(val = []) {//数据处理
				let result = []
				val.forEach((item) => {
					let temp = []
					temp['id'] = item.id
					temp['text'] = item.title
					temp['checked'] = false
					result.push(temp)
				})
				return result
			},
			//保存人群分组
			submitRenq() {
				if (this.crowdType.length <= 0) {
					return this.$common.msg('请选择人群标签！')
				}
				let update = {
					patientId: this.patientId, //（患者id）
					crowdType: this.gender[this.genderIndex].dictValue, //（人群类型）
					crowdLabel: this.crowdType.join(';'), //（人群标签-以英文分号‘;’分隔）
				}
				updateCrowdTag(update).then(res => {
					if (res.code == 200) {
						this.tags = [];
						this.crowdType = [];
						// this.getList(this.info.patientId)
						// this.$refs.guipopup.close();
						this.$emit('renqSubmit',true)
						this.$forceUpdate();
					} else {
						this.$common.msg('保存失败，请重新保存。')
					}
				})
			},
			rqclose() {
				this.$emit('rqclose',true)
			},
		}
	}
</script>
<style scoped>

</style>