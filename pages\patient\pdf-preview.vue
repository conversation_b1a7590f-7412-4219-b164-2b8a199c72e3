<template>
  <view class="pdf-content">
    <vue-pdf-embed
        annotation-layer
        text-layer
        :source="{
		        url: path,
		        cMapUrl: fontLink,
		        cMapPacked: true,
		      }"

        :height="'95vh'"
        :width="375"
        @loaded="handlePdfMsg"
    />
    <!-- <view style="position: absolute;bottom: 20rpx;">
      <button @click="lastPages()">上一页</button>
      <button @click="nextPages()">下一页</button>
    </view> -->
  </view>
</template>

<script>
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
import { previewNutritionInterventionPDF } from '@/api/patient'
export default {
  components: {
    VuePdfEmbed,
  },
  data() {
    return {
      // pdf地址
      path: "",
      // 文字链接
      fontLink: "https://unpkg.com/pdfjs-dist@2.12.313/cmaps/",
      // 分页
      pages: 1,
      sumPages: 0,
      // 患者ID和就诊ID
      patientId: "",
      visitId: ""
    }
  },
  onLoad(option) {
    console.log('PDF预览页面接收到的参数:', option)

    // 如果直接传入了PDF路径，使用传入的路径
    if (option.path) {
      this.path = option.path
    } else if (option.patientId && option.visitId) {
      // 如果传入的是患者ID和就诊ID，构建PDF URL
      this.patientId = option.patientId
      this.visitId = option.visitId
      this.getPdfUrl()
    }

    uni.setNavigationBarTitle({
      title:'营养干预方案报告'
    })
  },
  methods: {
    // 获取PDF预览地址
    getPdfUrl() {
      if (!this.patientId || !this.visitId) {
        uni.showToast({
          title: '缺少必要参数',
          icon: 'none'
        })
        return
      }

      console.log('开始请求PDF，参数:', { patientId: this.patientId, visitId: this.visitId })

      previewNutritionInterventionPDF({
        patientId: this.patientId,
        visitId: this.visitId
      }).then(res => {
        console.log('PDF API响应:', res)
        console.log('响应类型:', typeof res)
        console.log('是否为Blob:', res instanceof Blob)

        // 如果返回的是二进制文件流，创建blob URL
        if (res instanceof Blob) {
          console.log('处理Blob响应')
          this.path = URL.createObjectURL(res)
        } else if (res.data instanceof Blob) {
          console.log('处理res.data Blob响应')
          this.path = URL.createObjectURL(res.data)
        } else if (res.data && res.data.url) {
          console.log('处理URL响应')
          this.path = res.data.url
        } else if (res.data) {
          console.log('处理二进制数据响应')
          // 如果返回的是ArrayBuffer或其他二进制数据
          const blob = new Blob([res.data], { type: 'application/pdf' })
          this.path = URL.createObjectURL(blob)
        } else {
          console.log('未知响应格式，尝试直接使用')
          const blob = new Blob([res], { type: 'application/pdf' })
          this.path = URL.createObjectURL(blob)
        }
        console.log('最终PDF预览URL:', this.path)
      }).catch(error => {
        console.error('PDF加载失败:', error)
        uni.showToast({
          title: 'PDF加载失败: ' + (error.message || '未知错误'),
          icon: 'none'
        })
      })

      console.log('PDF预览URL:', this.path)
    },
    lastPages() {
      if(this.pages > 1) {
        this.pages -= 1
      }
    },
    nextPages() {
      if(this.pages < this.sumPages) {
        this.pages += 1
      } else {
        this.pages = 1
      }
    },
    // 获取pdf信息 numPages 总页数
    handlePdfMsg(val) {
      this.sumPages = val._pdfInfo.numPages
    }
  },

}
</script>

<style>
</style>
