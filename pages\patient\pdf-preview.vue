<template>
  <view class="pdf-content">
    <vue-pdf-embed
        annotation-layer
        text-layer
        :source="{
		        url: path,
		        cMapUrl: fontLink,
		        cMapPacked: true,
		      }"

        :height="'95vh'"
        :width="375"
        @loaded="handlePdfMsg"
    />
    <!-- <view style="position: absolute;bottom: 20rpx;">
      <button @click="lastPages()">上一页</button>
      <button @click="nextPages()">下一页</button>
    </view> -->
  </view>
</template>

<script>
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
import { previewNutritionInterventionPDF } from '@/api/patient'
export default {
  components: {
    VuePdfEmbed,
  },
  data() {
    return {
      // pdf地址
      path: "",
      // 文字链接
      fontLink: "https://unpkg.com/pdfjs-dist@2.12.313/cmaps/",
      // 分页
      pages: 1,
      sumPages: 0,
      // 患者ID和就诊ID
      patientId: "",
      visitId: ""
    }
  },
  onLoad(option) {
    console.log('PDF预览页面接收到的参数:', option)

    // 如果直接传入了PDF路径，使用传入的路径
    if (option.path) {
      this.path = option.path
    } else if (option.patientId && option.visitId) {
      // 如果传入的是患者ID和就诊ID，构建PDF URL
      this.patientId = option.patientId
      this.visitId = option.visitId
      this.getPdfUrl()
    }

    uni.setNavigationBarTitle({
      title:'营养干预方案报告'
    })
  },
  methods: {
    // 获取PDF预览地址
    getPdfUrl() {
      if (!this.patientId || !this.visitId) {
        uni.showToast({
          title: '缺少必要参数',
          icon: 'none'
        })
        return
      }

      // 构建PDF预览URL
      const baseUrl = this.$common.domain || ''
      const token = uni.getStorageSync('mbtoken') || ''

      // 在URL中添加Authorization参数
      // this.path = `${baseUrl}/zwb/nutritionIntervention/previewPDF?patientId=${this.patientId}&visitId=${this.visitId}&Authorization=${encodeURIComponent('Bearer ' + token)}`
      

      console.log('PDF预览URL:', this.path)
    },
    lastPages() {
      if(this.pages > 1) {
        this.pages -= 1
      }
    },
    nextPages() {
      if(this.pages < this.sumPages) {
        this.pages += 1
      } else {
        this.pages = 1
      }
    },
    // 获取pdf信息 numPages 总页数
    handlePdfMsg(val) {
      this.sumPages = val._pdfInfo.numPages
    }
  },

}
</script>

<style>
</style>
