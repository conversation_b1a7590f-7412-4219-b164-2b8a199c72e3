{"_from": "@vue/compiler-core@3.4.27", "_id": "@vue/compiler-core@3.4.27", "_inBundle": false, "_integrity": "sha512-E+RyqY24KnyDXsCuQrI+mlcdW3ALND6U7Gqa/+bVwbcpcR3BRRIckFoz7Qyd4TTlnugtwuI7YgjbvsLmxb+yvg==", "_location": "/@vue/compiler-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-core@3.4.27", "name": "@vue/compiler-core", "escapedName": "@vue%2fcompiler-core", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/", "/@vue/compiler-dom", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.4.27.tgz", "_shasum": "e69060f4b61429fe57976aa5872cfa21389e4d91", "_spec": "@vue/compiler-core@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerCore", "compat": true, "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.24.4", "@vue/shared": "3.4.27", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}, "deprecated": false, "description": "@vue/compiler-core", "devDependencies": {"@babel/types": "^7.24.0"}, "exports": {".": {"types": "./dist/compiler-core.d.ts", "node": {"production": "./dist/compiler-core.cjs.prod.js", "development": "./dist/compiler-core.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-core.esm-bundler.js", "import": "./dist/compiler-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "name": "@vue/compiler-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-core"}, "types": "dist/compiler-core.d.ts", "version": "3.4.27"}