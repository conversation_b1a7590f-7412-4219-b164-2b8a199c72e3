/**
* @vue/reactivity v3.4.27
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
var VueReactivity=function(t){"use strict";
/*! #__NO_SIDE_EFFECTS__ */function e(t,e){const s=new Set(t.split(","));return t=>s.has(t)}const s=()=>{},n=Object.assign,i=Object.prototype.hasOwnProperty,r=(t,e)=>i.call(t,e),c=Array.isArray,o=t=>"[object Map]"===f(t),u=t=>"function"==typeof t,a=t=>"symbol"==typeof t,l=t=>null!==t&&"object"==typeof t,h=Object.prototype.toString,f=t=>h.call(t),_=t=>f(t).slice(8,-1),d=t=>"string"==typeof t&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,p=(t,e)=>!Object.is(t,e);let v,g;class y{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=v,!t&&v&&(this.index=(v.scopes||(v.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const e=v;try{return v=this,t()}finally{v=e}}}on(){v=this}off(){v=this.parent}stop(t){if(this._active){let e,s;for(e=0,s=this.effects.length;e<s;e++)this.effects[e].stop();for(e=0,s=this.cleanups.length;e<s;e++)this.cleanups[e]();if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].stop(!0);if(!this.detached&&this.parent&&!t){const t=this.parent.scopes.pop();t&&t!==this&&(this.parent.scopes[this.index]=t,t.index=this.index)}this.parent=void 0,this._active=!1}}}function w(t,e=v){e&&e.active&&e.effects.push(t)}class R{constructor(t,e,s,n){this.fn=t,this.trigger=e,this.scheduler=s,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,w(this,n)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,j();for(let t=0;t<this._depsLength;t++){const e=this.deps[t];if(e.computed&&(S(e.computed),this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),x()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=E,e=g;try{return E=!0,g=this,this._runnings++,b(this),this.fn()}finally{k(this),this._runnings--,g=e,E=t}}stop(){this.active&&(b(this),k(this),this.onStop&&this.onStop(),this.active=!1)}}function S(t){return t.value}function b(t){t._trackId++,t._depsLength=0}function k(t){if(t.deps.length>t._depsLength){for(let e=t._depsLength;e<t.deps.length;e++)L(t.deps[e],t);t.deps.length=t._depsLength}}function L(t,e){const s=t.get(e);void 0!==s&&e._trackId!==s&&(t.delete(e),0===t.size&&t.cleanup())}let E=!0,O=0;const m=[];function j(){m.push(E),E=!1}function x(){const t=m.pop();E=void 0===t||t}function I(){O++}function P(){for(O--;!O&&A.length;)A.shift()()}function T(t,e,s){if(e.get(t)!==t._trackId){e.set(t,t._trackId);const s=t.deps[t._depsLength];s!==e?(s&&L(s,t),t.deps[t._depsLength++]=e):t._depsLength++}}const A=[];function M(t,e,s){I();for(const n of t.keys()){let s;n._dirtyLevel<e&&(null!=s?s:s=t.get(n)===n._trackId)&&(n._shouldSchedule||(n._shouldSchedule=0===n._dirtyLevel),n._dirtyLevel=e),n._shouldSchedule&&(null!=s?s:s=t.get(n)===n._trackId)&&(n.trigger(),n._runnings&&!n.allowRecurse||2===n._dirtyLevel||(n._shouldSchedule=!1,n.scheduler&&A.push(n.scheduler)))}P()}const V=(t,e)=>{const s=new Map;return s.cleanup=t,s.computed=e,s},W=new WeakMap,z=Symbol(""),N=Symbol("");function D(t,e,s){if(E&&g){let e=W.get(t);e||W.set(t,e=new Map);let n=e.get(s);n||e.set(s,n=V((()=>e.delete(s)))),T(g,n)}}function C(t,e,s,n,i,r){const u=W.get(t);if(!u)return;let l=[];if("clear"===e)l=[...u.values()];else if("length"===s&&c(t)){const t=Number(n);u.forEach(((e,s)=>{("length"===s||!a(s)&&s>=t)&&l.push(e)}))}else switch(void 0!==s&&l.push(u.get(s)),e){case"add":c(t)?d(s)&&l.push(u.get("length")):(l.push(u.get(z)),o(t)&&l.push(u.get(N)));break;case"delete":c(t)||(l.push(u.get(z)),o(t)&&l.push(u.get(N)));break;case"set":o(t)&&l.push(u.get(z))}I();for(const c of l)c&&M(c,4);P()}const K=e("__proto__,__v_isRef,__isVue"),H=new Set(Object.getOwnPropertyNames(Symbol).filter((t=>"arguments"!==t&&"caller"!==t)).map((t=>Symbol[t])).filter(a)),Y=F();function F(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const s=Pt(this);for(let e=0,i=this.length;e<i;e++)D(s,0,e+"");const n=s[e](...t);return-1===n||!1===n?s[e](...t.map(Pt)):n}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){j(),I();const s=Pt(this)[e].apply(this,t);return P(),x(),s}})),t}function G(t){a(t)||(t=String(t));const e=Pt(this);return D(e,0,t),e.hasOwnProperty(t)}class q{constructor(t=!1,e=!1){this._isReadonly=t,this._isShallow=e}get(t,e,s){const n=this._isReadonly,i=this._isShallow;if("__v_isReactive"===e)return!n;if("__v_isReadonly"===e)return n;if("__v_isShallow"===e)return i;if("__v_raw"===e)return s===(n?i?Lt:kt:i?bt:St).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=c(t);if(!n){if(o&&r(Y,e))return Reflect.get(Y,e,s);if("hasOwnProperty"===e)return G}const u=Reflect.get(t,e,s);return(a(e)?H.has(e):K(e))?u:(n||D(t,0,e),i?u:Nt(u)?o&&d(e)?u:u.value:l(u)?n?Ot(u):Et(u):u)}}class B extends q{constructor(t=!1){super(!1,t)}set(t,e,s,n){let i=t[e];if(!this._isShallow){const e=xt(i);if(It(s)||xt(s)||(i=Pt(i),s=Pt(s)),!c(t)&&Nt(i)&&!Nt(s))return!e&&(i.value=s,!0)}const o=c(t)&&d(e)?Number(e)<t.length:r(t,e),u=Reflect.set(t,e,s,n);return t===Pt(n)&&(o?p(s,i)&&C(t,"set",e,s):C(t,"add",e,s)),u}deleteProperty(t,e){const s=r(t,e),n=Reflect.deleteProperty(t,e);return n&&s&&C(t,"delete",e,void 0),n}has(t,e){const s=Reflect.has(t,e);return a(e)&&H.has(e)||D(t,0,e),s}ownKeys(t){return D(t,0,c(t)?"length":z),Reflect.ownKeys(t)}}class J extends q{constructor(t=!1){super(!0,t)}set(t,e){return!0}deleteProperty(t,e){return!0}}const Q=new B,U=new J,X=new B(!0),Z=new J(!0),$=t=>t,tt=t=>Reflect.getPrototypeOf(t);function et(t,e,s=!1,n=!1){const i=Pt(t=t.__v_raw),r=Pt(e);s||(p(e,r)&&D(i,0,e),D(i,0,r));const{has:c}=tt(i),o=n?$:s?At:Tt;return c.call(i,e)?o(t.get(e)):c.call(i,r)?o(t.get(r)):void(t!==i&&t.get(e))}function st(t,e=!1){const s=this.__v_raw,n=Pt(s),i=Pt(t);return e||(p(t,i)&&D(n,0,t),D(n,0,i)),t===i?s.has(t):s.has(t)||s.has(i)}function nt(t,e=!1){return t=t.__v_raw,!e&&D(Pt(t),0,z),Reflect.get(t,"size",t)}function it(t){t=Pt(t);const e=Pt(this);return tt(e).has.call(e,t)||(e.add(t),C(e,"add",t,t)),this}function rt(t,e){e=Pt(e);const s=Pt(this),{has:n,get:i}=tt(s);let r=n.call(s,t);r||(t=Pt(t),r=n.call(s,t));const c=i.call(s,t);return s.set(t,e),r?p(e,c)&&C(s,"set",t,e):C(s,"add",t,e),this}function ct(t){const e=Pt(this),{has:s,get:n}=tt(e);let i=s.call(e,t);i||(t=Pt(t),i=s.call(e,t)),n&&n.call(e,t);const r=e.delete(t);return i&&C(e,"delete",t,void 0),r}function ot(){const t=Pt(this),e=0!==t.size,s=t.clear();return e&&C(t,"clear",void 0,void 0),s}function ut(t,e){return function(s,n){const i=this,r=i.__v_raw,c=Pt(r),o=e?$:t?At:Tt;return!t&&D(c,0,z),r.forEach(((t,e)=>s.call(n,o(t),o(e),i)))}}function at(t,e,s){return function(...n){const i=this.__v_raw,r=Pt(i),c=o(r),u="entries"===t||t===Symbol.iterator&&c,a="keys"===t&&c,l=i[t](...n),h=s?$:e?At:Tt;return!e&&D(r,0,a?N:z),{next(){const{value:t,done:e}=l.next();return e?{value:t,done:e}:{value:u?[h(t[0]),h(t[1])]:h(t),done:e}},[Symbol.iterator](){return this}}}}function lt(t){return function(...e){return"delete"!==t&&("clear"===t?void 0:this)}}function ht(){const t={get(t){return et(this,t)},get size(){return nt(this)},has:st,add:it,set:rt,delete:ct,clear:ot,forEach:ut(!1,!1)},e={get(t){return et(this,t,!1,!0)},get size(){return nt(this)},has:st,add:it,set:rt,delete:ct,clear:ot,forEach:ut(!1,!0)},s={get(t){return et(this,t,!0)},get size(){return nt(this,!0)},has(t){return st.call(this,t,!0)},add:lt("add"),set:lt("set"),delete:lt("delete"),clear:lt("clear"),forEach:ut(!0,!1)},n={get(t){return et(this,t,!0,!0)},get size(){return nt(this,!0)},has(t){return st.call(this,t,!0)},add:lt("add"),set:lt("set"),delete:lt("delete"),clear:lt("clear"),forEach:ut(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{t[i]=at(i,!1,!1),s[i]=at(i,!0,!1),e[i]=at(i,!1,!0),n[i]=at(i,!0,!0)})),[t,s,e,n]}const[ft,_t,dt,pt]=ht();function vt(t,e){const s=e?t?pt:dt:t?_t:ft;return(e,n,i)=>"__v_isReactive"===n?!t:"__v_isReadonly"===n?t:"__v_raw"===n?e:Reflect.get(r(s,n)&&n in e?s:e,n,i)}const gt={get:vt(!1,!1)},yt={get:vt(!1,!0)},wt={get:vt(!0,!1)},Rt={get:vt(!0,!0)},St=new WeakMap,bt=new WeakMap,kt=new WeakMap,Lt=new WeakMap;function Et(t){return xt(t)?t:mt(t,!1,Q,gt,St)}function Ot(t){return mt(t,!0,U,wt,kt)}function mt(t,e,s,n,i){if(!l(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const r=i.get(t);if(r)return r;const c=(o=t).__v_skip||!Object.isExtensible(o)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(_(o));var o;if(0===c)return t;const u=new Proxy(t,2===c?n:s);return i.set(t,u),u}function jt(t){return xt(t)?jt(t.__v_raw):!(!t||!t.__v_isReactive)}function xt(t){return!(!t||!t.__v_isReadonly)}function It(t){return!(!t||!t.__v_isShallow)}function Pt(t){const e=t&&t.__v_raw;return e?Pt(e):t}const Tt=t=>l(t)?Et(t):t,At=t=>l(t)?Ot(t):t;class Mt{constructor(t,e,s,n){this.getter=t,this._setter=e,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new R((()=>t(this._value)),(()=>zt(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!n,this.__v_isReadonly=s}get value(){const t=Pt(this);return t._cacheable&&!t.effect.dirty||!p(t._value,t._value=t.effect.run())||zt(t,4),Wt(t),t.effect._dirtyLevel>=2&&zt(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function Vt(t,e,n=!1){let i,r;const c=u(t);c?(i=t,r=s):(i=t.get,r=t.set);return new Mt(i,r,c||!r,n)}function Wt(t){var e;E&&g&&(t=Pt(t),T(g,null!=(e=t.dep)?e:t.dep=V((()=>t.dep=void 0),t instanceof Mt?t:void 0)))}function zt(t,e=4,s){const n=(t=Pt(t)).dep;n&&M(n,e)}function Nt(t){return!(!t||!0!==t.__v_isRef)}function Dt(t){return Ct(t,!1)}function Ct(t,e){return Nt(t)?t:new Kt(t,e)}class Kt{constructor(t,e){this.__v_isShallow=e,this.dep=void 0,this.__v_isRef=!0,this._rawValue=e?t:Pt(t),this._value=e?t:Tt(t)}get value(){return Wt(this),this._value}set value(t){const e=this.__v_isShallow||It(t)||xt(t);t=e?t:Pt(t),p(t,this._rawValue)&&(this._rawValue=t,this._value=e?t:Tt(t),zt(this,4))}}function Ht(t){return Nt(t)?t.value:t}const Yt={get:(t,e,s)=>Ht(Reflect.get(t,e,s)),set:(t,e,s,n)=>{const i=t[e];return Nt(i)&&!Nt(s)?(i.value=s,!0):Reflect.set(t,e,s,n)}};class Ft{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:e,set:s}=t((()=>Wt(this)),(()=>zt(this)));this._get=e,this._set=s}get value(){return this._get()}set value(t){this._set(t)}}class Gt{constructor(t,e,s){this._object=t,this._key=e,this._defaultValue=s,this.__v_isRef=!0}get value(){const t=this._object[this._key];return void 0===t?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return function(t,e){const s=W.get(t);return s&&s.get(e)}(Pt(this._object),this._key)}}class qt{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function Bt(t,e,s){const n=t[e];return Nt(n)?n:new Gt(t,e,s)}const Jt=Vt;return t.EffectScope=y,t.ITERATE_KEY=z,t.ReactiveEffect=R,t.ReactiveFlags={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw"},t.TrackOpTypes={GET:"get",HAS:"has",ITERATE:"iterate"},t.TriggerOpTypes={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},t.computed=Vt,t.customRef=function(t){return new Ft(t)},t.deferredComputed=Jt,t.effect=function(t,e){t.effect instanceof R&&(t=t.effect.fn);const i=new R(t,s,(()=>{i.dirty&&i.run()}));e&&(n(i,e),e.scope&&w(i,e.scope)),e&&e.lazy||i.run();const r=i.run.bind(i);return r.effect=i,r},t.effectScope=function(t){return new y(t)},t.enableTracking=function(){m.push(E),E=!0},t.getCurrentScope=function(){return v},t.isProxy=function(t){return!!t&&!!t.__v_raw},t.isReactive=jt,t.isReadonly=xt,t.isRef=Nt,t.isShallow=It,t.markRaw=function(t){return Object.isExtensible(t)&&((t,e,s,n=!1)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:n,value:s})})(t,"__v_skip",!0),t},t.onScopeDispose=function(t){v&&v.cleanups.push(t)},t.pauseScheduling=I,t.pauseTracking=j,t.proxyRefs=function(t){return jt(t)?t:new Proxy(t,Yt)},t.reactive=Et,t.readonly=Ot,t.ref=Dt,t.resetScheduling=P,t.resetTracking=x,t.shallowReactive=function(t){return mt(t,!1,X,yt,bt)},t.shallowReadonly=function(t){return mt(t,!0,Z,Rt,Lt)},t.shallowRef=function(t){return Ct(t,!0)},t.stop=function(t){t.effect.stop()},t.toRaw=Pt,t.toRef=function(t,e,s){return Nt(t)?t:u(t)?new qt(t):l(t)&&arguments.length>1?Bt(t,e,s):Dt(t)},t.toRefs=function(t){const e=c(t)?new Array(t.length):{};for(const s in t)e[s]=Bt(t,s);return e},t.toValue=function(t){return u(t)?t():Ht(t)},t.track=D,t.trigger=C,t.triggerRef=function(t){zt(t,4)},t.unref=Ht,t}({});
