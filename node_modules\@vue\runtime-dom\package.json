{"_from": "@vue/runtime-dom@3.4.27", "_id": "@vue/runtime-dom@3.4.27", "_inBundle": false, "_integrity": "sha512-ScOmP70/3NPM+TW9hvVAz6VWWtZJqkbdf7w6ySsws+EsqtHvkhxaWLecrTorFxsawelM5Ys9FnDEMt6BPBDS0Q==", "_location": "/@vue/runtime-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/runtime-dom@3.4.27", "name": "@vue/runtime-dom", "escapedName": "@vue%2fruntime-dom", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.4.27.tgz", "_shasum": "fe8d1ce9bbe8921d5dd0ad5c10df0e04ef7a5ee7", "_spec": "@vue/runtime-dom@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueRuntimeDOM", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/runtime-core": "3.4.27", "@vue/shared": "3.4.27", "csstype": "^3.1.3"}, "deprecated": false, "description": "@vue/runtime-dom", "exports": {".": {"types": "./dist/runtime-dom.d.ts", "node": {"production": "./dist/runtime-dom.cjs.prod.js", "development": "./dist/runtime-dom.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-dom.esm-bundler.js", "import": "./dist/runtime-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-dom#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/runtime-dom.esm-bundler.js", "name": "@vue/runtime-dom", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-dom"}, "sideEffects": false, "types": "dist/runtime-dom.d.ts", "unpkg": "dist/runtime-dom.global.js", "version": "3.4.27"}