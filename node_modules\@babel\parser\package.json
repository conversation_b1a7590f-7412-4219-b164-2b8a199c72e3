{"_from": "@babel/parser@7.24.6", "_id": "@babel/parser@7.24.6", "_inBundle": false, "_integrity": "sha512-eNZXdfU35nJC2h24RznROuOpO94h6x8sg9ju0tT9biNtLZ2vuP8SduLqqV+/8+cebSLV9SJEAN5Z3zQbJG/M+Q==", "_location": "/@babel/parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/parser@7.24.6", "name": "@babel/parser", "escapedName": "@babel%2fparser", "scope": "@babel", "rawSpec": "7.24.6", "saveSpec": null, "fetchSpec": "7.24.6"}, "_requiredBy": ["/", "/@vue/compiler-core", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.6.tgz", "_shasum": "5e030f440c3c6c78d195528c3b688b101a365328", "_spec": "@babel/parser@7.24.6", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bin": {"parser": "bin/babel-parser.js"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A+parser+%28babylon%29%22+is%3Aopen"}, "bundleDependencies": false, "deprecated": false, "description": "A JavaScript parser", "devDependencies": {"@babel/code-frame": "^7.24.6", "@babel/helper-check-duplicate-nodes": "^7.24.6", "@babel/helper-fixtures": "^7.24.6", "@babel/helper-string-parser": "^7.24.6", "@babel/helper-validator-identifier": "^7.24.6", "@babel/types": "^7.24.6", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.0.0"}, "files": ["bin", "lib", "typings/babel-parser.d.ts", "index.cjs"], "homepage": "https://babel.dev/docs/en/next/babel-parser", "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "type": "commonjs", "types": "./typings/babel-parser.d.ts", "version": "7.24.6"}