{"_from": "magic-string@0.30.10", "_id": "magic-string@0.30.10", "_inBundle": false, "_integrity": "sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==", "_location": "/magic-string", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "magic-string@0.30.10", "name": "magic-string", "escapedName": "magic-string", "rawSpec": "0.30.10", "saveSpec": null, "fetchSpec": "0.30.10"}, "_requiredBy": ["/", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.10.tgz", "_shasum": "123d9c41a0cb5640c892b041d4cfb3bd0aa4b39e", "_spec": "magic-string@0.30.10", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "deprecated": false, "description": "Modify strings, generate sourcemaps", "devDependencies": {"@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "benchmark": "^2.1.4", "bumpp": "^9.4.0", "conventional-changelog-cli": "^3.0.0", "eslint": "^8.57.0", "mocha": "^10.4.0", "prettier": "^3.2.5", "publint": "^0.2.7", "rollup": "^3.29.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21"}, "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}}, "files": ["dist/*", "index.d.ts", "README.md"], "homepage": "https://github.com/rich-harris/magic-string#readme", "jsnext:main": "./dist/magic-string.es.mjs", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "license": "MIT", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "name": "magic-string", "packageManager": "pnpm@9.0.1", "repository": {"type": "git", "url": "git+https://github.com/rich-harris/magic-string.git"}, "scripts": {"bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "lint": "eslint src test && publint", "lint:fix": "eslint src test --fix", "prepare": "npm run build", "prepublishOnly": "npm run lint && rm -rf dist && npm test", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "test": "mocha", "watch": "rollup -cw"}, "types": "./dist/magic-string.cjs.d.ts", "version": "0.30.10"}