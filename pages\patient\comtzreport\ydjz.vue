<template>
	<!-- 中医饮食指导（儿科一般指导） -->
	<view class="fs-32">
		<view class="my-20 fs-32">
			<view class="d-flex mb-20" style="justify-content: space-between;">
				<view>姓名：{{info.name || '-'}}</view>
				<view>性别：{{info.sex == 1 ? "男" : "女"}}</view>
				<view>年龄：{{info.age || '-'}}岁</view>
			</view>
			<view>时间：{{list.length ? $common.parseTime(list[0].createTime) : '-'}}</view>
		</view>
		<uni-table :loading="loading" border stripe emptyText="暂无更多数据">
			<!-- 表头行 -->
			<uni-tr>
				<uni-th align="center" width="100rpx" id="lineTd">
					<view style="margin-top:60rpx;margin-left: 16rpx;">时间</view>
					<view style="margin-top:-60rpx;margin-left: -16rpx">时长</view>
				</uni-th>
				<uni-th align="center" width="150rpx" v-for="(item,index) in list.map(i=>i.remark)"
					:key="index">{{item}}</uni-th>
			</uni-tr>
			<!-- 表格数据行 -->
			<uni-tr>
				<uni-td align="center">周一</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.monday)" :key="index">{{item}}</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td align="center">周二</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.tuesday)" :key="index">{{item}}</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td align="center">周三</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.wednesday)" :key="index">{{item}}</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td align="center">周四</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.thursday)" :key="index">{{item}}</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td align="center">周五</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.friday)" :key="index">{{item}}</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td align="center">周六</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.saturday)" :key="index">{{item}}</uni-td>
			</uni-tr>
			<uni-tr>
				<uni-td align="center">周日</uni-td>
				<uni-td align="center" v-for="(item,index) in list.map(i=>i.sunday)" :key="index">{{item}}</uni-td>
			</uni-tr>
		</uni-table>
	</view>
</template>

<script>
	import {
		listFatloss
	} from '@/api/patient.js'
	export default {
		name: 'ysjy',
		props: ['templateId', 'visitRecordId', 'templateDictKey', 'info', 'patientId'],
		data() {
			return {
				loading: false,
				cardObj: uni.getStorageSync('mbcardObj'),
				list: []
			}
		},
		watch: {
			templateDictKey: {
				handler(newLength, oldLength) {
					this.getDetail();
				},
				immediate: true
			}
		},
		methods: {
			// 获取建议详情
			getDetail() {
				this.loading = true
				listFatloss({
					visitId: this.visitRecordId,
					patientId: this.patientId
				}).then(res => {
					this.list = res.rows
					this.loading = false
				})
			}
		},
	}
</script>

<style>
	.tle {
		width: 10%;
		border: 1px solid #f2f3f4;
		text-align: center;
		writing-mode: vertical-lr;
		/* 从左向右 从右向左是 writing-mode: vertical-rl;*/
		writing-mode: tb-lr;
		/*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
		letter-spacing: 10rpx;
		line-height: 70rpx;
		/* font-size: 28rpx; */
	}

	.gui-td {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 20rpx 10rpx;
		display: flexbox;
	}

	.gui-td-text {
		line-height: 40rpx !important;
		/* font-size: 24rpx; */
	}

	#lineTd {
		padding: 0;
		background: #fff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxsaW5lIHgxPSIwIiB5MT0iMCIgeDI9IjEwMCUiIHkyPSIxMDAlIiBzdHJva2U9IiNFQkVFRjUiIHN0cm9rZS13aWR0aD0iMSIvPjwvc3ZnPg==) no-repeat 100% center;
	}
</style>