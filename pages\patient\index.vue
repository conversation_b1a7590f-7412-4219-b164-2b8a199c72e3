<template>
	<view>
		<!-- <gui-page @reload="reload" :fullPage="true" :isLoading="pageLoading" :apiLoadingStatus="apiLoadingStatus" :loadmore="true" @loadmorefun="getList" ref="guiPage"> -->
		<gui-page ref="guipage" :isLoading="pageLoading" :refresh="true" @reload="reload">
			<view slot="gBody" class="gui-flex1 gui-flex gui-columns " style="position: fixed;">
				<!-- 局部选项卡 使用 切换导航 + swiper 实现 -->
				<view class="demo-nav gui-flex " style="">
					<view class="demo-nav-my" @click="navchange(2)">
						<view :class="currentIndex==2?'demo-nav-my-name-i':'demo-nav-my-name'">在管患者</view>
						<view :class="currentIndex==2?'nav-active-line':'nav-active-line-i'"></view>
					</view>
					<view class="demo-nav-my" @click="navchange(0)">
						<view :class="currentIndex==0?'demo-nav-my-name-i':'demo-nav-my-name'">门诊患者</view>
						<view :class="currentIndex==0?'nav-active-line':'nav-active-line-i'"></view>
					</view>
					<view class="demo-nav-my" v-if="selectMenu1.length>0" @click="navchange(1) ">
						<view :class="currentIndex==1?'demo-nav-my-name-i':'demo-nav-my-name'"
							v-if="userInfo.roles.indexOf('doctor') != -1">我的团队患者</view>
						<!-- <view :class="currentIndex==1?'demo-nav-my-name-i':'demo-nav-my-name'" >全部团队</view> -->
						<gui-select-menu v-else :titleColor="currentIndex==1?'#7784eb':'#2B2E3D'" :activeColor="'#7784eb'"
							:current="currentIndex" :items="selectMenu1" @select="select1" :selectIndex="selectIndex1"
							ref="selectMenu2">
						</gui-select-menu>
						<view :class="currentIndex==1?'nav-active-line':'nav-active-line-i'"></view>
					</view>
					<!-- <gui-switch-navigation v-if="roles=='doctor'"  :items="tabs" :size="150" fontSize="30rpx" activeFontSize="30rpx"  :currentIndex="currentIndex" @change="navchange">
					</gui-switch-navigation> -->
				</view>
				<view class="file" @click="fileClick">建档</view>
				<!--  :disable-touch="true" -->
				<swiper :current="currentIndex" @change="swiperChange"
					:style="{height:mainHeight+'px', width:'750rpx'}">
					<!-- 轮播项目数量对应 上面的选项标签 -->
					<swiper-item v-for="item in tabs.length">
						<view class="">
							<view class="search-warp gui-border-box  gui-flex gui-space-between gui-align-items-center gui-margin mb-20">
								<gui-search placeholder="姓名/就诊卡号/身份证号/诊断名称" @inputting="inputting"
									@confirm="inputConfirm()" :kwd="queryParams.condition" class="l-border"
									@clear="clearinit"></gui-search>
								<!-- <text @tap="showScreening"
									class="l-button gui-icons mygui-sbutton-loading gui-padding gui-border-radius-large"
									slot="default">筛选</text> -->
								<!-- 关闭清除按钮示例 -->
								<!-- gui-search @inputting="inputting" @confirm="confirm" :clearBtn="false"></gui-search -->
							</view>
							<!-- <view class="gui-flex mt-20 pb-10 px-40 fs-36 gui-space-around"
								style="border-bottom: 1rpx solid #e1e0e0;">
								<view class="pb-5 px-10" @click="changeData(1)"
									:class="changeDataType==1?'changeDataIon':''">今日</view>
								<view class="pb-5 px-10" @click="changeData(2)"
									:class="changeDataType==2?'changeDataIon':''">昨日</view>
								<view v-if="currentIndex==0" class="pb-5 px-10" @click="changeData(3)"
									:class="changeDataType==3?'changeDataIon':''">近3天</view>
								<view v-if="currentIndex!=0" class="pb-5 px-10" @click="changeData(3)"
									:class="changeDataType==3?'changeDataIon':''">全 部</view>
							</view> -->
							<scroll-view
								style=" flex: 1; width: 100%;overflow-y: scroll;-webkit-overflow-scrolling: touch;height: auto;background-color: #f0f0f0;"
								:scroll-y="true" :style="{height:mainHeight+'px'}" :refresher-threshold="100"
								@scrolltolower="scrolltolowerFun" refresher-enabled="true"
								refresher-default-style="none">
								<view class="gui-flex  gui-columns mx-20 mt-20" v-if="list.length > 0">
									<view style="padding: 20rpx;" class="gui-bg-white w-100 mb-20 gui-border-radius" v-for="(item,index) in list">
										<view class="gui-list-items border-b pb-20">
											<view class="gui-flex gui-columns gui-align-items-center">
												<view v-if="item.sex == 2" class="gui-list-image ucenter-face gui-relative">
													<image class="gui-list-image ucenter-face-image" style="height: 160rpx;"
														src="@/static/images/avatar-girl.png" mode="widthFix">
													</image>
												</view>
												<view v-else-if="item.sex == 1"
													class="gui-list-image ucenter-face gui-relative">
													<image class="gui-list-image ucenter-face-image" style="height: 160rpx;"
														src="@/static/images/avatar-man.png" mode="widthFix">
													</image>
												</view>
												<view v-else class="gui-list-image ucenter-face gui-relative">
													<image class="gui-list-image ucenter-face-image" style="height: 160rpx;"
														src="@/static/images/weizhi.png" mode="widthFix">
													</image>
												</view>
												<!-- <view class="">
													<view @click.stop="openRenq(item)" class="fs-24 px-15 py-10 gui-bold gui-border-radius mt-10 w120 gui-flex gui-justify-content-center" style="color:#7784eb ; border: 2rpx solid #7784eb;">
														+分组
													</view>
												</view> -->
											</view>
											<view class="gui-list-body" @click="toDetails(item)"
												style="padding: 0 !important;">
												<view class="gui-flex gui-space-between mb-10">
													<view class="gui-flex">
														<text
															class="gui-list-title-text gui-bold gui-primary-color mr-15">{{item.name}}</text>
															<text style="flex:1;" v-if="item.isBindingWx==1"
																class="gui-list-body-desc gui-text-small gui-color-black gui-block-text gui-relative mr-10">
																<!-- @click="toConsult(item.patientId)" -->
																<image style="width: 20px;height: 20px; "
																	class="gui-list-image"
																	src="@/static/images/wx.png" mode="widthFix">
																</image>
																<view v-if="item.isSubscribe==1" class="gui-bg-red"
																	style="width: 18rpx; height: 18rpx;border-radius: 18rpx; position: absolute;top: -5rpx;right: -5rpx;">
																</view>
															</text>
														<gui-tags v-if="item.crowdName" :text="item.crowdName"
															bgClass="gui-bg-purple" :size="20"
															:borderRadius="50"></gui-tags>
														<gui-tags v-if="item.evaluation=== '1'" text="好转"
															bgClass="gui-bg-blue" :size="20" :borderRadius="50"></gui-tags>
														<gui-tags v-if="item.evaluation=== '2'" text="一般"
															bgClass="gui-bg-blue" :size="20" :borderRadius="50"></gui-tags>
														<gui-tags v-if="item.evaluation=== '3'" text="无效"
															bgClass="gui-bg-blue" :size="20" :borderRadius="50"></gui-tags>
														<gui-tags v-if="item.evaluation=== '4'" text="显著"
															bgClass="gui-bg-blue" :size="20" :borderRadius="50"></gui-tags>
														<gui-tags v-if="item.evaluation=== '5'" text="痊愈"
															bgClass="gui-bg-blue" :size="20" :borderRadius="50"></gui-tags>
													</view>
												</view>
												<view
													class="gui-list-body-desc gui-color-gray gui-block-text flex-1">{{sexlitsFin(item) || '未知'}} {{item.age}}岁 {{item.tel}}</view>
												<!-- <view @tap.stop="open1(item.patientId,item.name)" v-if="item.isBindingWx==1"
													style="float: right; margin-top: -50rpx; padding-right: 25px;">
													<text style="flex:1;"
														class="gui-list-body-desc gui-text-small gui-color-black gui-block-text mt-10 gui-relative">
														<image style="width: 24px;height: 24px; "
															class="gui-list-image"
															src="@/static/images/wx.png" mode="widthFix">
														</image>
														<view v-if="item.isSubscribe==1" class="gui-bg-red"
															style="width: 20rpx; height: 20rpx;border-radius: 20rpx; position: absolute;top: 0rpx;right: 0rpx;">
														</view>
													</text>
													<text style="flex:1;font-weight: 400; font-size:12px;"
														class="gui-text-small gui-block-text demo gui-color-black">
														会 话
													</text>
												</view> -->
												<view @click.stop="openGuipopup(item)"
													style="float: right; margin-top: -50rpx; padding-right: 25px;"
													v-if="currentIndex!=0 && item.isBindingWx!=1">
													<text style="flex:1;"
														class="gui-list-body-desc gui-text-small gui-color-black gui-block-text mt-10">
														<image style="width: 35px;height: 35px; "
															class="gui-list-image"
															src="@/static/images/erw.png" mode="widthFix">
														</image>
													</text>
												</view>
												<text v-if="item.visitCardNum.length>15" style="padding-top: 10px ;"
													class="gui-list-body-desc gui-color-blue gui-block-text">就诊卡号：{{item.visitCardNum && item.visitCardNum.replace(/(\d{6})\d*([0-9a-zA-Z]{2})/,"$1******$2")}}</text>
												<text v-else style="padding-top: 10px ;"
													class="gui-list-body-desc gui-color-blue gui-block-text">就诊卡号：{{item.visitCardNum}}</text>
												<view class="gui-flex pt-10" v-if="item.westDiagTile">
													门诊诊断：
													<view class="flex-1 ">
														<text class="ellipsis-2">{{item.westDiagTile ||'-'}}</text>
														<!-- 中医诊断
														<text class="ellipsis-1">{{item.chinaDiagTile}}</text> -->
													</view>
												</view>
											</view>
										</view>
										<view class="gui-flex gui-justify-content-end mt-10">
											<view @click="toTongue(item)" class="fs-24 px-15 py-10 gui-bold gui-border-radius mt-10 w120 gui-flex gui-justify-content-center mr-20" style="color:#7784eb ; border: 2rpx solid #7784eb;">
												+舌象
											</view>
											<view @click.stop="openRenq(item)" class="fs-24 px-15 py-10 gui-bold gui-border-radius mt-10 w120 gui-flex gui-justify-content-center mr-20" style="color:#7784eb ; border: 2rpx solid #7784eb;">
												+分组
											</view>
											<view @tap.stop="open1(item.patientId,item.name)" v-if="item.isBindingWx==1" class="fs-24 px-15 py-10 gui-bold gui-border-radius mt-10 w120 gui-flex gui-justify-content-center mr-20" style="color:#7784eb ; border: 2rpx solid #7784eb;">
												<text class="gui-icons">&#xe6b8; 会话</text>
											</view>
										</view>
									</view>
									<view class="mt-20 text-center">{{loadingStatus}}</view>
									<view class="mt-200"></view>
								</view>
								<!-- <gui-empty v-else>
									<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
										<image class="gui-empty-img"
											src="https://images.weserv.nl/?url=https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png">
										</image>
									</view>
									<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
										style="color:#9DABFF;">暂无数据 ......</text>
								</gui-empty> -->
							</scroll-view>
						</view>
					</swiper-item>
				</swiper>
				<gui-modal ref="guimodal1" closeBtnStyle="display:none;" :canCloseByShade="false" :title="title"
					title-style="font-size:20px;padding:20px; font-weight:600;">
					<view slot="content" class="gui-padding gui-bg-gray">
						<view style="padding: 15px;">
							<textarea auto-height="true" maxlength="280" type="text"
								style="width: 100%; font-size: 18px;" class="gui-form-input" v-model="content"
								name="content" placeholder="请输入内容" />
						</view>
					</view>
					<!-- 利用 flex 布局 可以放置多个自定义按钮哦  -->
					<view slot="btns" class="gui-flex gui-rows gui-space-between">
						<view class="modal-btns gui-flex1" style="margin-right:80rpx;" @tap="close1">
							<text class="modal-btns gui-color-gray"
								style="font-size: 18px;padding-left: 40px;">取消</text>
						</view>
						<view class="modal-btns gui-flex1" style="margin-left:80rpx;" @tap="confirm1">
							<text class="modal-btns gui-color-blue"
								style="font-size: 18px;padding-right: 40px;">确认</text>
						</view>
					</view>
				</gui-modal>
				<!-- 人群标签底部弹出 -->
				<gui-popup ref="rqguipopup" position="bottom" :zIndex='999'>
					<renqunFZ :genderArr="genderArr" :patientId="patientIdFZ" @rqclose="rqclose" @renqSubmit="renqSubmit" :openType="2"></renqunFZ>
				</gui-popup>
				<!-- 居中弹出 -->
				<gui-popup ref="guipopup" :width="'680rpx'" :bgColor="'rgba(0, 0, 0, 0.9)'">
					<view class="gui-relative gui-box-shadow ">
						<!-- <text
							class="gui-block-text demo-close gui-icons gui-color-gray gui-absolute-rt mt-30 mr-30 fs-60"
							@tap.stop="closeGuipopup">&#xe78a;</text> -->
						<view class="gui-bg-white gui-border-radius">
							<view class="py-20 px-30">
								<image :src="wxQrcodeUrl || '../../static/images/erw.png'"
									style="height: 620rpx;width: 620rpx;" mode=""></image>
							</view>
							<view class="text-center pb-40 fs-32 gui-bold" v-if="popupData.name">
								【 <text class="pr-40">{{popupData.name || ''}}</text>{{popupData.visitCardNum || ''}} 】
							</view>
						</view>
						<text class="gui-block-text demo-close gui-icons gui-color-white fs-60 mt-20"
							style="text-align: center; font-size: 80rpx;" @tap.stop="closeGuipopup">&#xe78a;</text>
					</view>
				</gui-popup>
				<!-- 右侧弹出筛选 -->
				<gui-popup ref="guipopupright" position="right" width="630rpx" :top="rightAraeTop">
					<view class="gui-bg-white right-area gui-box-shadow">
						<scroll-view :scroll-y="true">
							<view class="px-20 ">
								<view class="gui-margin-top font-bold ">
									<text class="gui-text">年龄：</text>
								</view>
								<view class="input-row">
									<input @blur="inputBlur" class="startinput" v-model="start" placeholder="开始年龄" />
									<!-- <view class="line"></view> -->
									至
									<input @blur="inputBlur" class="endinput" v-model="end" placeholder="结束年龄" />
								</view>
								<view class="gui-margin-top font-bold">
									<text class="gui-text">患者体质：</text>
								</view>
								<view style="margin-top:15rpx;">
									<gui-stags checkedBg="gui-bg-zdy" :tags="tizs" :padding="30" :lineHeight="2.5"
										@change="physicalChange" type="checkbox"></gui-stags>
								</view>
								<view class="font-bold">
									<text class="gui-text">人群类型：</text>
								</view>
								<view style="margin-top:15rpx;">
									<gui-stags checkedBg="gui-bg-zdy" :tags="leixs" :padding="30" :lineHeight="2.5"
										@change="crowdTypeChange" type="checkbox"></gui-stags>
								</view>
								<!-- <view v-for="(item,index) in leixs" style="margin-top: 15rpx;">
									<uni-data-checkbox multiple v-model="item.checkedCities"  :map="map" :localdata="[item]" @change="val=>{change(val,item)}"></uni-data-checkbox>
								</view> -->
							</view>
						</scroll-view>
						<view class="gui-flex gui-justify-content-center ">
							<view class="right-area-footer-btn gui-bg-gray mr-20 b-radius-20" hover-class="gui-tap"
								@tap="screeningReset">
								<text class="right-area-footer-btn gui-block-text gui-color-gray">重置</text>
							</view>
							<view class="right-area-footer-btn btncolor b-radius-20" hover-class="gui-tap"
								@tap="screeningSubmit">
								<text class="right-area-footer-btn gui-block-text gui-color-white ">搜索</text>
							</view>
						</view>
					</view>
				</gui-popup>
				<gui-area-picker ref="graceAddressPicker" @confirm="fileConfirm" :value="default2"
					v-if="default2 != null"></gui-area-picker>
				<gui-modal ref="modalFile" closeBtnStyle="display:none;" :canCloseByShade="false" :width="'660rpx'"
					title="建档" title-style="font-size:20px;padding:20px; font-weight:600;">
					<view slot="content" class="gui-padding gui-bg-white">
						<scroll-view scroll-y="true" style="max-height: 60vh;">
							<view class="pb-40">
								<view
									class="search-warp gui-border-box  gui-flex gui-space-between gui-align-items-center">
									<!-- @inputting="inputFile" @confirm="initFile()" -->
									<gui-search @inputting="inputFile" placeholder="就诊卡号/身份证号" :kwd="hisIsitCardNum"
										class="modl-border" @clear="clearinit(2)"></gui-search>
									<text @tap="hisPatient"
										class="modl-button gui-icons mygui-sbutton-loading gui-border-radius-large"
										slot="default">HIS获取</text>
									<!-- 关闭清除按钮示例 -->
									<!-- gui-search @inputting="inputting" @confirm="confirm" :clearBtn="false"></gui-search -->
								</view>
								<view class="mt-30 fs-32">
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex w-60 gui-align-items-center">
											<view class="gui-flex1 w-30 mr-15" style="text-align-last:justify;"><text class="gui-color-red">*</text>姓名</view>
											<view class="w-60 gui-bg-gray py-10 px-15">
												<input type="text" style="font-size: 32rpx;" class="" maxlength="10"
													disabled v-model="hisData.name" name="userName"
													placeholder="HIS获取" />
											</view>
										</view>
										<view class="gui-flex w-40 gui-align-items-center pl-15">
											<view class="gui-flex1 w-30">性 别</view>
											<view class="gui-bg-gray py-10 px-15" style="width: 64%;">
												<input type="text" style="font-size: 32rpx;" maxlength="10" disabled
													v-model="hisData.sex==1?'男':hisData.sex==2?'女':'未知'"
													name="sex" placeholder="HIS获取" />
											</view>
										</view>
									</view>
									<!-- 身份证号 -->
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex1 gui-flex gui-align-items-center">
											<view class="gui-flex1"><text class="gui-color-red">*</text>身份证号</view>
											<view class="gui-bg-gray py-10 px-15" style="width: 76%;">
												<input type="text" style="font-size: 32rpx;" maxlength="18" :disabled="true"
													v-model="hisData.idCard" name="idCard" placeholder="HIS获取" />
											</view>
										</view>
									</view>
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex1 gui-flex w-60 gui-align-items-center">
											<view class="gui-flex1 w-40">出生日期</view>
											<view class="w-60 gui-bg-gray py-10 px-15">
												<input type="text" style="font-size: 32rpx;" maxlength="20" disabled
													v-model="hisData.birthday" name="birthday" placeholder="HIS获取" />
											</view>
										</view>
										<view class="gui-flex w-40 gui-align-items-center pl-15">
											<view class="gui-flex1">年 龄</view>
											<view class="gui-bg-gray py-10 px-15" style="width: 64%;">
												<input type="number" style="font-size: 32rpx;" maxlength="3" disabled
													v-model="hisData.age" name="age" placeholder="HIS获取" />
											</view>
										</view>
									</view>
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex1 gui-flex gui-align-items-center">
											<view class="gui-flex1"><text class="gui-color-red">*</text>本人电话</view>
											<view class="py-10 px-15" style="width: 76%;">
												<input @blur=" val =>{ phoneInput(val,1)}" type="text" style="font-size: 32rpx;" maxlength="11"
													v-model="hisData.tel" name="tel" placeholder="手机号" />
											</view>
										</view>
									</view>
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex1 gui-flex gui-align-items-center">
											<view class="gui-flex1">家属电话</view>
											<view class="py-10 px-15" style="width: 76%;">
												<input @blur=" val =>{ phoneInput(val,2)}" type="text" style="font-size: 32rpx;" maxlength="11"
													v-model="hisData.contactsTel" name="contactsTel" placeholder="手机号" />
											</view>
										</view>
									</view>
									<view class="gui-form-item gui-border-b" @tap="fileOpen">
										<text class="gui-form-label fs-32" style="text-align-last:justify;">现地址</text>
										<view class="gui-form-body" style="margin-left: 30rpx;">
											<input type="text" class="gui-form-input" :value="hisData.district" disabled
												placeholder="请选择" style="font-size: 32rpx;"/>
										</view>
										<text class="gui-icons gui-form-icon gui-color-gray gui-text-right px-20">&#xe603;</text>
									</view>
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex1 gui-flex gui-align-items-center">
											<view class="gui-flex1">详细地址</view>
											<view class="py-10 px-15" style="width: 76%;">
												<textarea auto-height="true" maxlength="280" type="text"
													style="width: 100%; font-size: 32rpx;" v-model="hisData.address" name="address"
													placeholder="请输入详细地址" />
											</view>
										</view>
									</view>
									<view class="gui-flex gui-border-b py-10">
										<view class="gui-flex1 gui-flex gui-align-items-center">
											<view class="gui-flex1"><text class="gui-color-red">*</text>责任医生</view>
											<view v-if="doctorData.length>0" style="width: 76%;padding-left: 15rpx;">
												<picker v-model="hisData.doctor" :range="doctorData" @change="pickerDoctor"
													range-key="nickName">
													<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
														<text class="gui-text fs-32">{{doctorIndex == -1?'请选择':doctorData[doctorIndex].nickName}}</text>
														<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
													</view>
												</picker>
											</view>
										</view>
									</view>
									<view class="gui-flex gui-border-b py-10">
										<view  class="gui-flex1 gui-flex gui-align-items-center">
											<view class="gui-flex1"><text class="gui-color-red">*</text>责任护士</view>
											<view v-if="nurseData.length>0" style="width: 76%;padding-left: 15rpx;">
												<picker v-model="hisData.nurse" :range="nurseData" @change="pickerNurse"
													range-key="nickName">
													<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
														<text class="gui-text fs-32">{{nurseIndex == -1?'请选择':nurseData[nurseIndex].nickName}}</text>
														<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
													</view>
												</picker>
											</view>
										</view>
									</view>
								</view>
							</view>
						</scroll-view>
					</view>
					<!-- 利用 flex 布局 可以放置多个自定义按钮哦  -->
					<view slot="btns" class="gui-flex gui-rows gui-space-between">
						<view class="modal-btns gui-bg-gray gui-flex1" @tap="closeFile">
							<text class="modal-btns gui-color-gray"
								style="font-size: 18px;">取 消</text>
						</view>
						<view class="modal-btns gui-bg-blue gui-flex1" @tap="confirmFile">
							<text class="modal-btns gui-color-white"
								style="font-size: 18px;">保存建档</text>
						</view>
					</view>
				</gui-modal>
			</view>
		</gui-page>
	</view>
</template>
<script>
	import renqunFZ from './component/renqunFZ.vue'
	import jadeCheckbox from '@/components/jade-checkbox/jade-checkbox.vue'
	import utils from '@/tools/utils/utils.js'
	import {
		addArchival,
		getAppointPatientlist,
		getDutyUserList,
    getConfigKey
	} from '@/api/patient.js'
	var graceJS = require('@/GraceUI5/js/grace.js');
	export default {
		components: {
			jadeCheckbox,renqunFZ
		},
		data() {
			return {
				patientIdFZ:'',//人群分组的患者id
				genderArr : [],//人群分组-人群类型的数据
				doctorData: [], //建档弹窗的责任医生数据
				doctorIndex: -1,
				nurseData: [], //建档弹窗的责任护士数据
				nurseIndex: -1,
				default2: null,
				hisIsitCardNum: '',
				padStart: false, // 启用自动补零功能，将从系统参数中获取
				hisData: {
					district: '',
				}, //his获取回来的数据
				// 用于记录是否有 api 请求正在执行
				apiLoadingStatus: false,
				wxQrcodeUrl: '', //患者太阳码
				popupData: {}, //太阳码显示信息
				isRefresher: false, //下拉刷新状态
				hasNextPage: false, //是否还有下一页
				loadingStatus: '', //加载状态
				userInfo: {}, //用户信息
				changeDataType: 3, //查询范围 1、今日 ， 2、昨日 3、近3天
				roles: [], //角色
				selectIndex1: 0,
				selectMenu1: [],
				start: '',
				end: '',
				map: {
					text: 'dictLabel',
					value: 'dictValue'
				},
				checkTitle: '全选/反选',
				listp: [],
				package_ids: [],
				// 性别
				sexlits: [],
				// 全屏 loading
				pageLoading: true,
				//分页
				list: [],
				// 查询参数
				queryParams: {
					dataType: 3, //时间查询范围 1、今日 ， 2、昨日 3、近3天
					type: 2,
					pageNum: 1,
					pageSize: 10,
					seachType: 1,
					condition: '',
					physical: '',
					crowdType: '',
					properties: '',
					manageLevel: '',
					teamId: ''
				},
				crowdType: [],
				physical: [],
				patientId: undefined,
				// 选项卡标签
				tabs: [{
					id: 0,
					name: '门诊患者'
				}, {
					id: 3,
					name: '我的团队患者'
				}, {
					id: 1,
					name: '在管患者'
				}],
				// 选中选项的 索引
				currentIndex: 2,
				// 核心区域高度
				mainHeight: 680,
				startValue2: "请选择日期",
				endValue2: '',
				tizs: [],
				leixs: [],
				renqs: [],
				dengjs: [],
				sizeSed: null,
				// 右侧区高度
				rightAraeHeight: 200,
				// 右侧弹出层 top 值
				rightAraeTop: 0,
				loding: false,
				rqbqOptions: [],
				teamList: [],
				formData: [

				],
				title: "",
				content: undefined,
        // 是否发送短信
        signSmsSwitch: '',
		idCardRules: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))((0[1-9])|([1-2][0-9])|30|31)\d{3}[\dX]$/i
			}
		},
		onLoad: function() {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
			// 01. 获取页面主体高度
			graceJS.getRefs('guiPage', this, 0, (ref) => {
				ref.getDomSize('guiPageBody', (e) => {
					// 02. 导航的高度
					// 可以 使用 graceJS.select() 函数动态获取， 也可以使用 uni.upx2px 转换已知高度
					// 轮播主体高度 = 页面高度 - 导航高度
					this.mainHeight = e.height - uni.upx2px(150);
				});
			});
			var systemInfo = graceJS.system();
			// #ifdef H5
			this.rightAraeHeight = systemInfo.windowHeight + 44 - uni.upx2px(100);
			// #endif
			// #ifndef H5
			this.rightAraeHeight = systemInfo.windowHeight - uni.upx2px(100);
			// #endif
			this.init();
			this.$common.RequestData({ //性别信息
				url: `${this.$common.getDicts}sys_user_sex`,
				method: 'get',
			}, res => {
				this.sexlits = res.data
			})
			// this.getInint();
		},
		onShow() {
			// this.getList();
			this.getInint();
      getConfigKey("sign_sms_switch").then(res => {
        this.signSmsSwitch = res.msg
      })
      getConfigKey("padStart").then(res => {
        this.padStart = res.msg === 'true'
      })
		},
		methods: {
			openRenq(item){//分组
				this.$set(this,'patientIdFZ',item.patientId)
				this.$common.RequestData({
					url: this.$common.getCrowdType + 'people_type',
					data: {},
					method: 'get',
				}, res => {
					if (res.code == 200) {
						this.$set(this,'genderArr',res.data)
						this.$refs.rqguipopup.open();
					}else{
						this.$common.msg(res.msg)
					}
				})
			},
			//上传舌面
			toTongue(item) {
				let that = this
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'], //可以指定是原图还是压缩图，original 原图，compressed 压缩图
					sourceType: ['album', 'camera'],
					success: function(res) {
						const tempFilePaths = res.tempFilePaths;
						let resSize = res.tempFiles[0].size;
						console.log('图片信息大小==', resSize)
						// if (resSize < 1048576) {
						// 	console.log('正常图片，', tempFilePaths[0])
						// 	that.$common.navTo('/pages/patient/addTong?patientId=' + item.patientId +
						// 		'&tempFilePaths=' + tempFilePaths[0])
						// } else {
							console.log('压缩前图片，', tempFilePaths[0])
							utils.translateImg(tempFilePaths[0], 0.7, 'blobString', imgUrl => {
								// console.log('压缩后图片',imgUrl)
								uni.setStorageSync('baseFile', imgUrl)
								that.$common.navTo('/pages/patient/addTong?patientId=' + item
									.patientId + '&tempFilePaths=' + String(tempFilePaths[0]) +
									'&name=' + res.tempFiles[0].name)
							})
						// }
					}
				});
			},
			rqclose() {this.$refs.rqguipopup.close();},
			renqSubmit(e){//保存人群分组
				this.init()
				this.$refs.rqguipopup.close();
			},
			// picker 切换
			pickerDoctor(e) {
				this.doctorIndex = e.detail.value;
			},
			// picker 切换
			pickerNurse(e) {
				this.nurseIndex = e.detail.value;
				// this.formData.relationship = this.gender[this.genderIndex];
			},
			getInint() { //初始化
				this.getphy(); //患者体质
				this.getpeople(); //人群类型
				this.getTeamList(); //获取团队数据
				this.getInfo(); //角色信息
				this.getrqsx(); //人群属性
				this.getlevel(); //管理等级
				this.queryCrowdLabelList(); //人群标签
				this.init();
			},
			init() { //初始化
				this.navchange(this.currentIndex);
				this.$set(this, 'userInfo', uni.getStorageSync('mbuserInfo')) //用户信息
				this.queryParams.pageNum = 1;
				this.getList();
			},
			closeFile() {
				this.$refs.modalFile.close();
				this.hisData = {}
			},
			phoneInput(e,type,site){
				let phone = site?e:e.detail.value
				const regex = /^1[3-9]\d{9}$/;
				if (!regex.test(phone)) {
					if (type == 1) {
						this.hisData.tel = ''
					} else{
						this.hisData.contactsTel = ''
					}
					this.$common.msg('请输入正确的手机号！');
					return false;
				}else{
					return true;
				}

			},
			confirmFile() {
				if (!this.hisData.idCard || !this.hisData.name) {
					return this.$common.msg('请点击HIS获取到用户信息再保存！')
				}
				if (this.doctorIndex == -1) {
					return this.$common.msg('请选择责任医生！')
				}
				if (this.nurseIndex == -1) {
					return this.$common.msg('请选择责任护士！')
				}
				uni.showLoading({
					title: '保存中'
				});

				if (!this.phoneInput(this.hisData.tel,1,3)) {//校验本人手机号码
					return
				}
				if (this.hisData.contactsTel && this.hisData.contactsTel.length>1 &&!this.phoneInput(this.hisData.contactsTel,2,3)) {//校验家属手机号码
					return
				}
				addArchival({
					...this.hisData,
					deptId: this.userInfo.deptId,
					appId: this.userInfo.deptAppIds,
					dutyDoctorId: this.doctorData[this.doctorIndex].userId,
					dutyNurseId: this.nurseData[this.nurseIndex].userId,
					birthday: this.hisData.birthday + ' 00:00:00'
				}).then(res => {
					uni.hideLoading();
					if (res.code == 200) {
            this.reload();
            if (this.signSmsSwitch === '1') {
              this.$common.msg('建档成功，邀请短信已发送')
            } else {
              this.$common.msg('保存成功')
            }
						this.$refs.modalFile.close();
						this.hisData = {};
						this.hisIsitCardNum = '';
					} else {
						this.$common.msg('保存失败，请重新获取。')
						this.hisData = {}
					}
				})
			},
			fileOpen() {
				this.$refs.graceAddressPicker.open();
			},
			//搜索输入内容
			inputFile(e) {
				this.hisIsitCardNum = e;
			},
			hisPatient() { //his获取
				console.log('his获取按钮数据==', this.userInfo)
				var open = true;
				if (!this.hisIsitCardNum) {
					return this.$common.msg('请输入就诊卡号或身份证号再查询。')
				}

        // 住院号为8位数，就诊卡号为7位数，在等于8位数时不需要补0
				let searchCardNum = this.hisIsitCardNum;
				if (this.padStart && searchCardNum && searchCardNum.length < 12 && searchCardNum.length !== 8) {
					searchCardNum = searchCardNum.padStart(12, '0');
					// 更新显示的就诊卡号，让用户看到补零后的结果
					this.hisIsitCardNum = searchCardNum;
				}

				uni.showLoading({
					title: '加载中'
				});
				this.nurseIndex = -1;
				this.doctorIndex = -1;
				if (open) {
					open = false;
					getAppointPatientlist({
						visitCardNum: searchCardNum,
						deptId: this.userInfo.deptId,
						appId: this.userInfo.deptAppIds
					}).then(res => {
						if (res.data && res.data.patientId) {
							this.hisData = res.data
							this.hisData.sex = res.data.sex ? res.data.sex :''
							if (res.data.idCard && this.idCardRules.test(res.data.idCard)) {
								this.hisData.birthday = this.toIdCard(res.data.idCard, 1)
								this.hisData.age = this.toIdCard(res.data.idCard, 3)
								this.hisData.sex = this.toIdCard(res.data.idCard, 2)
							}
							var strings = []
							strings[0] = res.data.province ? res.data.province : '';
							strings[1] = res.data.city ? res.data.city : '';
							strings[2] = res.data.county ? res.data.county : '';
							this.hisData.district = strings.filter(str => str !== '' && str != 'null').join(',') ||
								'';
							if (res.data.nurseId) { //责任护士Id
								this.nurseIndex = this.nurseData.findIndex(item => item.userId == res.data.nurseId)
							}
							if (res.data.doctorId) { //责任医生Id
								this.doctorIndex = this.doctorData.findIndex(item => item.userId == res.data
									.doctorId)
							}
							// this.hisData.district = `${res.data.province != 'null'?res.data.province+',':''}${res.data.city?res.data.city+',':''}`
							uni.hideLoading();
							this.$forceUpdate()
							setTimeout(function() {
								open = true
							}, 1000);
						} else {
							uni.hideLoading();
							this.$common.msg('未查询到该患者，请检查。')
							setTimeout(function() {
								open = true
							}, 1000);
							// if (this.idCardRules.test(this.hisIsitCardNum)) {//医生端是否支持手工建档？？？
							// 	this.hisData.idCard = this.hisIsitCardNum;
							// 	this.hisData.birthday = this.toIdCard(this.hisIsitCardNum, 1)
							// 	this.hisData.age = this.toIdCard(this.hisIsitCardNum, 3)
							// 	this.hisData.sex = this.toIdCard(this.hisIsitCardNum, 2)
							// 	this.$forceUpdate()
							// }
						}

					})
				}
			},
			toIdCard(userCard, num) {
				console.log('身份证校验==',!this.idCardRules.test(userCard))
				if (!this.idCardRules.test(userCard)) {
				  this.$common.msg('请输入正确的身份证')
				  return
				}
				if (num == 1) {
					var birth = '';
					//获取出生日期
					birth = userCard.substr(6, 8).replace(/(.{4})(.{2})/, "$1-$2-");
					return birth;
				}
				if (num == 2) {
					//获取性别
					if (userCard.length < 17) {
						return "3";
					}
					if (parseInt(userCard.substr(16, 1)) % 2 == 1) {
						//男
						return "1";
					} else {
						//女
						return "2";
					}
				}
				if (num == 3) {
					//获取年龄
					var myDate = new Date();
					var month = myDate.getMonth() + 1;
					var day = myDate.getDate();
					var age = myDate.getFullYear() - userCard.substring(6, 10) - 1;
					if (userCard.substring(10, 12) < month || userCard.substring(10, 12) == month && userCard.substring(12,
							14) <= day) {
						age++;
					}
					return age;
				}
			},
			fileConfirm(e) {
				// 返回选中城市的编号及文本信息
				this.hisData.province = e.names[0];
				this.hisData.city = e.names[1];
				this.hisData.county = e.names[2];
				this.hisData.district = e.names[0] + ',' + e.names[1] + ',' + e.names[2];
				this.$forceUpdate()
			},
			fileClick() {
				this.hisIsitCardNum = '';
				getDutyUserList({
					deptId: this.userInfo.deptId,
					appId: this.userInfo.deptAppIds
				}).then(res => {
					// this.hisData = res.data
					this.doctorData = res.data.doctor;
					this.nurseData = res.data.nurse;
					// console.log('医生数据==',this.doctorData)
					// console.log('护士数据==',this.nurseData)
					if (res.data?.type) { //管理员角色时，接口没有返回字段type
						if (res.data.type == 2) {
							this.nurseIndex = this.nurseData.findIndex(item => item.nickName == this.userInfo
								.nickName)
							// console.log('nurseIndex==',this.nurseIndex)
						} else {
							this.doctorIndex = this.doctorData.findIndex(item => item.nickName == this.userInfo
								.nickName)
							// console.log('doctorIndex==',this.doctorIndex)
						}
					}
				})
				this.$refs.modalFile.open();
				setTimeout(() => {
					this.default2 = ['广西', '南宁市', '兴宁区'];
				}, 1000);
			},

			sexlitsFin(item) {
				var test = '未知'
				this.sexlits.find(i => {
					item.sex == i.dictValue
					if (item.sex == i.dictValue) {
						test = i.dictLabel
					}
				})
				return test
			},
			//第一行头部选择
			navchange: function(index) {
				this.currentIndex = index;
				this.queryParams.pageNum = 1;
				if (index == 0 && this.$refs.selectMenu2) {
					this.$refs.selectMenu2.close()
				}
				this.queryParams.seachType = this.tabs[index].id
				this.changeData(this.changeDataType)
			},
			changeData(e) { //日期范围选择 1、今日 ， 2、昨日 3、近3天
				this.changeDataType = e
				this.queryParams.pageNum = 1;
				this.queryParams.dataType = e
				this.getList()
			},

			inputConfirm() { //输入框的搜索
				this.list = [];
				this.queryParams.pageNum = 1;
				this.navchange(this.currentIndex)
			},
			//搜索输入内容
			inputting(e) {
				this.queryParams.condition = e;
				// this.init();
			},
			getInfo() {
				this.$common.RequestData({
					url: this.$common.getInfo,
					data: {},
					method: 'get',
				}, res => {
					this.roles = res.data.roles;
					console.log(this.roles)
				})
			},
			closeGuipopup() {
				this.$refs.guipopup.close();
				this.wxQrcodeUrl = ''
			},
			openGuipopup(item) {
				this.$common.RequestData({
					url: this.$common.createWxQrcode,
					data: {
						visitCardNum: item.visitCardNum
					},
					method: 'get',
				}, res => {
					if (res.code == 200) {
						this.popupData = {
							name: item.name,
							visitCardNum: item.visitCardNum
						}
						this.wxQrcodeUrl = this.$common.domain + '/' + res.data.qrcodeUrl
						this.$refs.guipopup.open();
					} else {
						this.$common.msg("图片获取失败，稍后重试~")
					}

				})
			},
			open1: function(patientId, patientName) {
				console.log('发起会话')
				this.$common.RequestData({
					url: this.$common.getFriendSession + patientId,
					method: 'get',
					data: {}
				}, res => {
					console.log('消息发送数据===', res.data)
					let imObj = {
						groupIndex: res.data.groupIndex, // 组ID
						friendName: res.data.uname, // 患者姓名
						avatar: res.data.uface, //患者头像
						// openid:uni.getStorageSync("mbhuanzInfo").,
						openid: res.data.uniappOpenId, //item.openid//患者openid    需要接口提供并修改再更新系统
						friendUid: res.data.friendUid, // 患者UUid
						orderId: res.data.orderId, // 订单id
						types: res.data.types, // 订单状态
						userUid: uni.getStorageSync("mbuserUid"), // 当前用户UUid
						// 标题头使用
						age: res.data.patientAge = null ? res.data.age : res.data.patientAge,
						name: res.data.patientName = null ? res.data.uname : res.data.patientName,
						gender: res.data.patientGender,
						patientId: res.data.patientId
						// gender:item.patientGender
					};
					// this.$common.navTo('/pages/chat/consult?imObj=' + encodeURIComponent(JSON.stringify(imObj)))
					this.$common.navTo('/pages/chat/consult')
					uni.setStorageSync('mbimObj', imObj)
				})
				// this.title="发起对"+patientName+"的会话"
				// event.cancelBubble=true;
				// this.patientId=patientId
				// this.formData.forEach(item=>{
				// 	if(item.patientId==this.patientId){
				// 		this.content=item.content;
				// 	}
				// })
				// this.$refs.guimodal1.open();
			},
			close1: function() {
				let patient = {
					"patientId": this.patientId,
					"content": this.content
				}
				this.formData.push(patient)
				this.content = undefined;
				this.$refs.guimodal1.close();
			},
			confirm1: function() {
				// 客户点击确认按钮后的逻辑请在此处实现
				if (this.content == undefined) {
					this.$common.msg("发送内容不能为空")
					return false
				}
				let item = {}
				console.log('发送的内容的患者id==', this.patientId)
				let sendMsg = {
					'content': this.content,
					'msgType': 'txt',
					'patientIds': [this.patientId]
				}
				this.patientId = undefined;
				this.formData.content = undefined;
				console.log('发送的内容===', sendMsg)
				this.$common.RequestData({
					url: this.$common.sendMsgBatch,
					data: sendMsg,
					method: 'post'
				}, res => {
					if (res.code == 200) {
						item = res.data
						console.log('消息发送数据===', item)
						let imObj = {
							groupIndex: item.groupIndex, // 组ID
							friendName: item.uname, // 患者姓名
							avatar: item.uface, //患者头像
							openid: item.uniappOpenId, //item.openid//患者openid    需要接口提供并修改再更新系统
							friendUid: item.friendUid, // 患者UUid
							orderId: item.orderId, // 订单id
							types: item.types, // 订单状态
							userUid: uni.getStorageSync("mbuserUid"), // 当前用户UUid
							// 标题头使用
							age: item.patientAge = null ? item.age : item.patientAge,
							name: item.patientName = null ? item.uname : item.patientName,
							gender: item.patientGender,
							patientId: item.patientId
						};
						this.$refs.guimodal1.close();
						this.content = undefined;
						// this.$common.navTo('/pages/chat/consult?imObj=' + encodeURIComponent(JSON.stringify(imObj)))
						this.$common.navTo('/pages/chat/consult')
						uni.setStorageSync('mbimObj', imObj)
					}
				})
			},
			getTeamList() {
				this.$common.RequestData({
					url: this.$common.getTeamList,
					data: {
						status: 1,
						deptId: uni.getStorageSync('mbuserInfo').deptId
					},
					method: 'get',
				}, res => {
					console.log('医生团队==', this.userInfo.roles)
					var list = []
					// this.teamList = res.data
					if (this.userInfo.roles.indexOf('doctor') != -1) {
						list.unshift({
							questionName: '我的团队患者',
							id: 3
						})
					} else {
						list = res.data
						list.unshift({
							questionName: '全部团队',
							id: 3
						})
					}
					this.selectMenu1 = list
				})
			},
			select1(index, val) {
				this.selectIndex1 = index
				this.queryParams.pageNum = 1;
				this.getList()
			},
			inputBlur() {
				if (Number(this.start) && Number(this.end) && Number(this.start) > Number(this.end)) {
					this.start = '0'
				}
			},
			changeClick(e, item) {
				item.checkedCities = e
				// this.$set(this.)
			},
			change(e, item) {
				item.checkedCities = e.detail.value
			},
			queryCrowdLabelList() {
				this.$common.RequestData({
					url: this.$common.queryCrowdLabelList,
					data: {},
					method: 'get',
				}, res => {
					for (let key in res.data) {
						res.data[key] = {
							list: res.data[key],
							checkedCities: []
						}
					}
					this.listp = res.data
					// this.sortDateFn(this.listp)
				})
			},
			// 清除搜索文字
			clearinit() {
				this.queryParams.condition = ''
				this.inputConfirm();
			},
			arr(val = []) {
				let result = []
				val.forEach((item) => {
					let temp = []
					temp['id'] = item.dictValue
					temp['text'] = item.dictLabel
					temp['checked'] = false
					result.push(temp)
				})
				return result
			},
			//患者体质
			getphy() {
				let dictType = "physical"
				this.$common.RequestData({
					url: this.$common.getphy + '/' + dictType,
					data: {},
					method: 'get',
				}, res => {
					let result = this.arr(res.data)
					this.tizs = result
				}, )
			},
			//人群类型
			getpeople() {
				// console.log('人群类型')
				let dictType = "people_type"
				this.$common.RequestData({
					url: this.$common.getphy + '/' + dictType,
					data: {},
					method: 'get',
				}, res => {
					let result = this.arr(res.data)
					this.leixs = result
				}, )
			},
			//人群属性
			getrqsx() {
				// console.log('人群属性')
				let dictType = "rqsx"
				this.$common.RequestData({
					url: this.$common.getphy + '/' + dictType,
					data: {},
					method: 'get',
				}, res => {
					let result = this.arr(res.data)
					this.renqs = result
				}, )
			},
			//管理等级
			getlevel() {
				// console.log('管理等级')
				let dictType = "fllow_manage_level"
				this.$common.RequestData({
					url: this.$common.getphy + '/' + dictType,
					data: {},
					method: 'get',
				}, res => {
					let result = this.arr(res.data)
					this.dengjs = result
				}, )
			},
			scroll: function(e) {
				this.old.scrollTop = e.detail.scrollTop
			},
			reload() {
				//下拉刷新
				this.queryParams.pageNum = 1;
				this.getList(true);
			},
			// 下拉刷新被触发
			refresherrefreshFun() {
				this.list = []
				this.queryParams.pageNum = 1
				this.getList(true)
			},
			// 下拉刷新触发
			refresherpullingFun() {
				this.isRefresher = true
			},
			//上拉加载，页面滑动到底部加载分页数据
			scrolltolowerFun() {
				if (this.hasNextPage) {
					this.queryParams.pageNum = this.queryParams.pageNum + 1
					this.loadingStatus = "上拉加载更多"
					this.getList()
				} else {
					this.$common.msg("已加载全部", 'success', 1000)
					this.loadingStatus = "已加载全部"
				}
			},
			//患者信息
			getList(isReload) {
				this.pageLoading = true;
				if (this.loding == true) {
					return
				}
				this.loding = true
				if (this.queryParams.seachType == 3 && this.selectIndex1 !== 0) {
					this.queryParams.teamId = this.selectMenu1[this.selectIndex1].id
				} else {
					this.queryParams.teamId = ''
				}
				this.queryParams.crowdType = this.crowdType.length > 0 ? this.crowdType.join(';') : ''
				this.queryParams.physical = this.physical.length > 0 ? this.physical.join(';') : ''
				this.queryParams.startAge = this.start
				this.queryParams.endAge = this.end
				if (this.currentIndex == 0) {
					this.queryParams.seachType = ''
					this.queryParams.pageSize = 1000
					this.$common.RequestData({
						url: this.$common.patientListHis,
						data: this.queryParams,
						method: "get"
					}, res => {
						if (res.code == 200) {
							this.list = res.rows
							console.log(this.list)
							this.pageLoading = false;
							this.loding = false
						}
					}, true, fail => {
						this.loding = false
					})
				} else {
					console.log('进来请求在管患者')
					this.apiLoadingStatus = true;
					this.queryParams.pageSize = 10
					this.$common.RequestData({
						url: this.$common.patientList,
						data: this.queryParams,
						method: "get"
					}, res => {
						var zongPage = Math.ceil(Number(res.total / this.queryParams.pageSize))
						if (isReload) {
							this.$refs.guipage.endReload();
						}
						if (this.queryParams.pageNum >= 2) {
							this.list = this.list.concat(res.rows)
							// if(this.queryParams.pageNum >= zongPage){
							// 	this.hasNextPage = false;
							// 	this.loding = false;
							// 	this.pageLoading = false;
							// 	return
							// }
						} else { // 第一页 有可能是第一次加载或者刷新
							this.list = res.rows;
						}
						this.hasNextPage = !res.total < this.queryParams.pageSize;
						//this.hasNextPage = true;
						this.pageLoading = false;
						console.log('this.pageLoading===', this.pageLoading)
						this.loding = false
					}, true, fail => {
						this.loding = false
					})
				}
			},

			swiperChange: function(e) {
				var index = e.detail.current;
				this.currentIndex = index;
				this.queryParams.seachType = this.tabs[index].id
				this.getList()
			},
			// 展示筛选界面
			showScreening: function() {
				this.$refs.guipopupright.open();
			},
			//选择就诊时间
			confirm2: function(res) {
				this.queryParams.startDate = res[0][0] + '-' + res[0][1] + '-' + res[0][2];
				this.queryParams.endDate = res[1][0] + '-' + res[1][1] + '-' + res[1][2];
				this.startValue2 = this.queryParams.startDate;
				this.endValue2 = this.queryParams.endDate;
			},
			// 选择患者体质
			physicalChange: function(e, data) {
				this.physical = []
				e.map(item => {
					this.physical.push(data[item].id);
				})

			},
			// 选择人群类型
			crowdTypeChange: function(e, data) {
				this.crowdType = []
				e.map(item => {
					this.crowdType.push(data[item].id);
				})
			},
			// 选择人群属性
			propertiesChange: function(e, data) {
				for (var i = 0; i < data.length; i++) {
					if (data[i].checked == true) {
						this.queryParams.properties = data[i].id;
					}
				}
			},
			// 选择管理等级
			manageLevelChange: function(e, data) {
				for (var i = 0; i < data.length; i++) {
					if (data[i].checked == true) {
						this.queryParams.manageLevel = data[i].id;
					}
				}
			},
			// 筛选提交
			screeningSubmit: function() {
				this.queryParams.seachType = this.tabs[this.currentIndex].id
				this.getList();
				this.$refs.guipopupright.close();
			},
			//详情页
			toDetails(item) {
				// this.$common.navTo('/pages/patient/details?openType=1&patientInfo=' + encodeURIComponent(JSON.stringify(item)))
				this.$common.navTo('/pages/patient/details?openType=1')
				uni.setStorageSync('mbdetailsPatientId', item.patientId)
			},
			// 重置筛选
			screeningReset: function() {
				const end = new Date();
				const start = new Date();
				start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
				this.getphy();
				this.getpeople();
				this.getrqsx();
				this.getlevel();
				// this.leixs.map(item=>{
				// 	item.checkedCities = []
				// })
				this.start = ''
				this.end = ''
				this.queryParams = {
					type: 2,
					pageNum: 1,
					pageSize: 100,
					seachType: 1,
					condition: '',
					// startDate: this.$common.parseTime(start, '{y}-{m}-{d}'),
					// endDate: this.$common.parseTime(end, '{y}-{m}-{d}'),
					physical: '',
					crowdType: '',
					properties: '',
					manageLevel: ''
				};
				this.crowdType = []
				this.physical = []
				this.startValue2 = this.queryParams.startDate
				this.endValue2 = this.queryParams.endDate
				// this.startValue2 = '请选择日期';
				// this.endValue2 = ''
			},
			// 性别字典
			sexDict(val) {
				if (val == 1) {
					return "男";
				} else if (val == 2) {
					return "女";
				} else {
					return "未知";
				}
			},
			//查看报告
			report: function() {
				this.$common.navTo('/pages/patient/report')
			},

		}
	}
</script>
<style scoped>
	.modl-button {
		text-align: center;
		color: #fff;
		width: 160rpx;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 28rpx;
	}

	.modl-border {
		width: 420rpx;
		border: 1rpx solid #a6a6a7;
	}
	.ucenter-face-image {
		width: 100rpx;
		height: 130rpx;
	}
	.file {
		z-index: 999;
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(to bottom, #8190ff, #7784eb);
		color: #fff;
		border-radius: 100%;
		display: flex;
		justify-content: center;
		position: fixed;
		bottom: 260rpx;
		right: 30rpx;
		align-items: center;
	}

	.changeDataIon {
		font-weight: bold;
		color: #7485eb;
	}

	.line {
		width: 50rpx;
		height: 2rpx;
		background: #999;
		margin: 0 20rpx;
	}

	.input-row {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-top: 15rpx;
	}

	.startinput {
		margin-right: 15rpx;
		font-size: 30rpx;
		width: 200rpx;
		height: 60rpx;
		padding: 0 10rpx;
		border: 2rpx solid #eee;
	}

	.endinput {
		margin-left: 15rpx;
		height: 60rpx;
		padding: 0 10rpx;
		font-size: 30rpx;
		width: 200rpx;
		border: 2rpx solid #eee;
	}

	/* 	>>>.uni-scroll-view-content {
		display: flex;
		justify-content: space-around;
	} */

	>>>.gui-scroll-x-items {
		align-items: center;
	}

	>>>.graceDateTime-main {
		width: 630rpx;
	}

	>>>.gui-list-body-desc {
		font-size: 28rpx;
	}

	>>>.gui-list-title-text {
		font-size: 30rpx;
		text-align: right;
	}

	>>>.gui-dateBT-shade {
		width: 630rpx;
	}

	.l-border {
		width: 100%;
		border: 1rpx solid #a6a6a7;
	}

	.content {
		padding-bottom: calc(var(--window-bottom) + 200rpx);
	}

	.l-button {
		text-align: center;
		color: #fff;
		width: 70rpx;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 28rpx;
	}

	.demo-nav-my {
		flex: 1;
		font-size: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.demo-nav-my-name-i {
		color: #7784eb;
		height: 50px;
		font-weight: bold;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.demo-nav-my-name {
		height: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.nav-active-line {
		width: 80rpx;
		height: 6rpx;
		background: linear-gradient(to right, #8190ff, #7784eb);
	}

	.nav-active-line-i {
		width: 80rpx;
		height: 6rpx;
		background: #fff
	}

	.demo-nav {
		display: flex;
		flex-direction: row;
		padding: 15rpx 30rpx;
	}

	/* .demo{width:600rpx; font-size:22rpx; text-align:center; border-radius:50rpx;} */
	/* 自定义吸顶元素样式 */
	.fixed-top-view {
		height: 100rpx;
	}

	.graceSelectMenuItem {
		width: 180rpx;
	}

	.right-area {
		flex: 1;
		height: 800rpx;
	}

	>>>.uni-swiper-wrapper {
		padding-bottom: calc(var(--window-bottom) + 100rpx);
	}

	/* .colorTags {
		width: 120rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		font-size: 26rpx;
		color: #FFFFFF;
		border-radius: 5rpx;
	} */

	/* .price-input {
		width: 120rpx;
		padding: 15rpx;
		line-height: 50rpx;
		height: 80rpx;
		font-size: 26rpx;
		text-align: center;
		background-color: #F5F6F8;
		border-radius: 5rpx;
	} */

	.right-area-footer {
		width: 580rpx;
		height: 100rpx;
		background-color: #F7F8FA;
	}

	.right-area-footer-btn {
		width: 160rpx;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		text-align: center;
	}

	.btncolor {
		background-color: #7784eb;
	}

	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}
	.uni-textarea-placeholder {
	    font-size: 32rpx;
	    line-height: 36rpx;
	}
</style>
