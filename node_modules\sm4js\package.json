{"_from": "sm4js", "_id": "sm4js@0.0.6", "_inBundle": false, "_integrity": "sha512-lKJWSaSksB4MbVpYXfb0cFRgTAjEAAZSxoMZ498BuGtwVQ5aB/23wJVxFLRIZQx9cyoVihK98FiOYH7VxIZyMg==", "_location": "/sm4js", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "sm4js", "name": "sm4js", "escapedName": "sm4js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/sm4js/-/sm4js-0.0.6.tgz", "_shasum": "d075e778e8d8c4089f0520ad88312799a8d365c0", "_spec": "sm4js", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "xishangrucai"}, "bundleDependencies": false, "dependencies": {"base64-js": "^1.3.1"}, "deprecated": false, "description": "An implementation of the browser and nodejs-server side of the sm4 algorithm", "keywords": ["nodejs", "sm4", "js", "browser", "algorithm"], "license": "MIT", "main": "./lib/sm4js.min.js", "name": "sm4js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "0.0.6"}