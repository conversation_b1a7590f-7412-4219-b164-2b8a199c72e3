 // 测试环境
export default function cofing(e){
 let domain
 let socketUrl
 let ddId
 let bingingWxUser
 let appid
 let agentid
 //星铭健康
 if(e == 1){
 	domain = 'http://zwb.frp.starup.net.cn/prod-api';//公司外网地址
 	socketUrl = 'wss://zwb.frp.starup.net.cn/ws';     //公司环境socket
 	bingingWxUser = false
 	ddId = ''
	appid = 'ww95d738e678e659ad';
	agentid = '1000018';
 }else if(e == 2){
 	domain = 'https://gyjk.starup.net.cn/prod-api';//贵阳医院地址
 	socketUrl = 'wss://gyjk.starup.net.cn/ws';     //贵阳医院环境socket
 	ddId = 'ding096c8e73e2c8f83335c2f4657eb6378f'
 	bingingWxUser = false
	appid = 'ww95d738e678e659ad';
	agentid = '1000018';
 }else if(e == 3){//慢病管理
	  //domain = 'http://mbgl.frp.starup.net.cn/prod-api';//慢病管理地址-公司测试环境
	//domain = 'http://192.168.1.71:8084/dev-api'
	domain = 'http://192.168.1.72:8880'
 	socketUrl = 'ws://mbgl.frp.starup.net.cn/ws';     //慢病管理socket-公司测试环境
	// if(window.location.protocol === 'https:'){
	// 	domain = 'https://mbgl.frp.starup.net.cn/prod-api';
	// 	socketUrl = 'wss://mbgl.frp.starup.net.cn/ws';
	// }
 	ddId = ''
 	bingingWxUser = false
	appid = 'ww95d738e678e659ad';
	agentid = '1000029';
 }else if(e == 4){//南宁中医院
 	domain = 'http://gyjk.frp.starup.net.cn/prod-api';//南宁中医院公司测试环境
 	socketUrl = 'ws://gyjk.frp.starup.net.cn/ws';     //南宁中医院socket-公司测试环境
 	ddId = ''
 	bingingWxUser = false
	appid = 'ww95d738e678e659ad';
	agentid = '1000018';
 }

 return {
  domain,
  socketUrl,
  ddId,
  bingingWxUser,
  appid,
  agentid
 }
}
