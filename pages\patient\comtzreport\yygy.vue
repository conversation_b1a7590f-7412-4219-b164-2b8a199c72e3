<template>
  <!-- 营养干预方案 -->
  <view class="fs-32">
    <view class="my-20 fs-32">
      <view class="d-flex mb-20" style="justify-content: space-between;">
        <view>姓名：{{info.name || '-'}}</view>
        <view>性别：{{info.sex == 1 ? "男" : "女"}}</view>
        <view>年龄：{{info.age || '-'}}岁</view>
      </view>
      <view class="d-flex" style="justify-content: space-between; align-items: center;">
        <view>时间：{{list.length ? $common.parseTime(list[0].createTime) : '-'}}</view>
        <button v-if="currentData.id" class="report-btn" @click="viewPdfReport">查看报告</button>
      </view>
    </view>

    <view v-if="currentData.id">
      <!-- 减重方案 -->
      <view class="section-title">{{currentData.weightLossPlanName || '-'}}减重方案</view>

      <!-- 基础信息 -->
      <view class="sub-title">基础信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="label">首诊时间：</text>
          <text>{{currentData.checkDate || '-'}}</text>
        </view>
        <view class="info-item">
          <text class="label">身高：</text>
          <text>{{currentData.height || '-'}}cm</text>
        </view>
        <view class="info-item">
          <text class="label">体重：</text>
          <text>{{currentData.weight || '-'}}kg</text>
        </view>
        <view class="info-item">
          <text class="label">BMI：</text>
          <text>{{currentData.bmi || '-'}}kg/m²</text>
        </view>
        <view class="info-item">
          <text class="label">体脂率：</text>
          <text>{{currentData.bodyFatRate || '-'}}%</text>
        </view>
        <view class="info-item">
          <text class="label">瘦体重：</text>
          <text>{{currentData.leanWeight || '-'}}kg</text>
        </view>
        <view class="info-item">
          <text class="label">骨骼肌：</text>
          <text>{{currentData.boneMass || '-'}}kg</text>
        </view>
        <view class="info-item">
          <text class="label">腰围：</text>
          <text>{{currentData.waistline || '-'}}cm</text>
        </view>
        <view class="info-item">
          <text class="label">腰臀比：</text>
          <text>{{currentData.waistHipRatio || '-'}}</text>
        </view>
        <view class="info-item">
          <text class="label">体重等级：</text>
          <text>{{currentData.weightLevel || '-'}}</text>
        </view>
        <view class="info-item">
          <text class="label">体脂率：</text>
          <text>{{currentData.bodyFatLevel || '-'}}</text>
        </view>
        <view class="info-item">
          <text class="label">基础代谢：</text>
          <text>{{currentData.basalMetabolism || '-'}}kcal</text>
        </view>
      </view>

      <!-- 基础疾病 -->
      <view class="sub-title">基础疾病</view>
      <view class="disease-list">
        <text v-if="currentData.hasHypertension == 1" class="disease-item">高血压</text>
        <text v-if="currentData.hasDiabetes == 1" class="disease-item">糖尿病</text>
        <text v-if="currentData.hasHeartDisease == 1" class="disease-item">冠心病</text>
        <text v-if="currentData.hasGout == 1" class="disease-item">痛风</text>
        <text v-if="currentData.hasChronicKidney == 1" class="disease-item">慢性肾病</text>
        <text v-if="currentData.hasLiverDysfunction == 1" class="disease-item">肝功能异常</text>
        <text v-if="currentData.hasThyroidDysfunction == 1" class="disease-item">甲状腺功能减退</text>
        <text v-if="!hasAnyDisease" class="no-disease">无</text>
      </view>
      <view v-if="currentData.otherDiseases" class="other-diseases">
        其他疾病：{{currentData.otherDiseases}}
      </view>

      <!-- 减重目标 -->
      <view class="sub-title">减重目标</view>
      <view class="target-grid">
        <view class="target-item">
          <text class="label">月末目标体重：</text>
          <text>{{currentData.targetWeightMonthly || '-'}}kg</text>
        </view>
        <view class="target-item">
          <text class="label">月末目标腰围：</text>
          <text>{{currentData.targetWaistlineMonthly || '-'}}cm</text>
        </view>
        <view class="target-item">
          <text class="label">长期目标体重：</text>
          <text>{{currentData.targetWeightLongterm || '-'}}kg</text>
        </view>
        <view class="target-item">
          <text class="label">长期目标腰围：</text>
          <text>{{currentData.targetWaistlineLongterm || '-'}}cm</text>
        </view>
      </view>

      <!-- 饮食方案 -->
      <view class="section-title">饮食方案</view>

      <!-- 减肥食谱制定 -->
      <view class="sub-title">食谱制定说明</view>
      <view v-if="currentData.recipeFormulationDescription" style="margin-bottom: 20rpx;">
        <!-- <view style="word-wrap: break-word; word-break: break-all; white-space: pre-wrap;"> -->
        {{currentData.recipeFormulationDescription}}
        <!-- </view> -->
      </view>

      <!-- 饮食方案表格 -->
      <!-- <view class="sub-title">饮食方案安排</view> -->
      <view v-if="currentData.dietDetailList && currentData.dietDetailList.length">
        <view v-for="(mealGroup, mealType) in groupedDietData" :key="mealType" class="meal-group">
          <view class="meal-header">{{mealType}}</view>
          <uni-table border stripe>
            <uni-tr>
              <uni-th align="center" width="80rpx">时间</uni-th>
              <uni-th align="center" width="120rpx">食物类别</uni-th>
              <uni-th align="center">食物内容</uni-th>
              <uni-th align="center" width="100rpx">加餐</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, index) in mealGroup" :key="index">
              <uni-td align="center">{{item.mealTime || '-'}}</uni-td>
              <uni-td align="center">{{item.foodCategory || '-'}}</uni-td>
              <uni-td style="word-wrap: break-word; word-break: break-all;">{{item.foodContent || '-'}}</uni-td>
              <uni-td align="center">
                <text :style="{color: item.extraMeal == 1 ? '#4caf50' : '#333'}">
                  {{item.extraMeal == 1 ? '是' : '否'}}
                </text>
              </uni-td>
            </uni-tr>
          </uni-table>
        </view>
      </view>

      <view class="sub-title">其它说明</view>
      <view v-if="currentData.dietOtherDescription">
        <view v-html="currentData.dietOtherDescription"></view>
      </view>

      <!-- 食谱执行说明 -->
      <view class="sub-title">食谱执行说明</view>
      <view v-if="currentData.dietGuidanceContent">
        <view v-html="currentData.dietGuidanceContent"></view>
      </view>

      <!-- 运动方案 -->
      <view class="section-title">运动方案</view>
      <!-- <view class="sub-title"></view> -->
      <view v-if="currentData.heartRateControl" class="diary-item">
        {{currentData.heartRateControl}}
      </view>

      <!-- 运动方案表格 -->
      <!-- <view class="sub-title">运动方案安排</view> -->
      <view v-if="currentData.exerciseDetailList && currentData.exerciseDetailList.length">
        <view v-for="(weekGroup, weekNumber) in groupedExerciseData" :key="weekNumber" class="week-group">
          <view class="week-header">{{weekNumber}}</view>
          <uni-table border stripe>
            <uni-tr>
              <uni-th align="center" width="100rpx">运动类型</uni-th>
              <uni-th align="center">运动项目</uni-th>
              <uni-th align="center" width="120rpx">周运动量</uni-th>
              <uni-th align="center" width="100rpx">运动时长</uni-th>
            </uni-tr>
            <uni-tr v-for="(item, index) in weekGroup" :key="index">
              <uni-td align="center">{{item.exerciseType || '-'}}</uni-td>
              <uni-td style="word-wrap: break-word; word-break: break-all;">{{item.exerciseContent || '-'}}</uni-td>
              <uni-td>{{item.highIntensity || '-'}}</uni-td>
              <uni-td align="center">{{item.exerciseDuration || '-'}}</uni-td>
            </uni-tr>
          </uni-table>
        </view>
      </view>

      <!-- 建议有氧运动项目 -->
      <view class="sub-title">建议有氧运动项目</view>
      <view v-if="currentData.suggestedAerobicExercises" class="exercise-content">
        <view v-html="currentData.suggestedAerobicExercises"></view>
      </view>


      <!-- 饮食和运动日记说明 -->
      <view class="sub-title">饮食和运动日记说明</view>
      <view class="diary-content">

        <!-- <view v-if="currentData.exercisePrecautions" class="diary-item">
          运动注意事项：{{currentData.exercisePrecautions}}
        </view> -->
        <view v-if="currentData.exerciseGuidanceContent" class="diary-item">
          <view v-html="currentData.exerciseGuidanceContent" style="display: inline;"></view>
        </view>
      </view>
    </view>

    <view v-else-if="!loading" class="no-data">
      暂无营养干预方案数据
    </view>
  </view>
</template>

<script>
import {
  listNutritionIntervention
} from '@/api/patient.js'

export default {
  name: 'yygy',
  props: ['templateId', 'visitRecordId', 'templateDictKey', 'info', 'patientId'],
  data() {
    return {
      loading: false,
      cardObj: uni.getStorageSync('mbcardObj'),
      list: [],
      currentData: {}
    }
  },
  computed: {
    hasAnyDisease() {
      return this.currentData.hasHypertension == 1 ||
          this.currentData.hasDiabetes == 1 ||
          this.currentData.hasHeartDisease == 1 ||
          this.currentData.hasGout == 1 ||
          this.currentData.hasChronicKidney == 1 ||
          this.currentData.hasLiverDysfunction == 1 ||
          this.currentData.hasThyroidDysfunction == 1
    },
    groupedDietData() {
      const groupedData = {};
      this.currentData.dietDetailList.forEach(item => {
        if (!groupedData[item.mealType]) {
          groupedData[item.mealType] = [];
        }
        groupedData[item.mealType].push(item);
      });
      return groupedData;
    },
    groupedExerciseData() {
      const groupedData = {};
      this.currentData.exerciseDetailList.forEach(item => {
        if (!groupedData[item.weekNumber]) {
          groupedData[item.weekNumber] = [];
        }
        groupedData[item.weekNumber].push(item);
      });
      return groupedData;
    }
  },
  watch: {
    templateDictKey: {
      handler(val) {
        if (val == 36) {
          this.getDetail();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取营养干预方案详情
    getDetail() {
      this.loading = true
      listNutritionIntervention({
        visitId: this.visitRecordId,
        patientId: this.patientId
      }).then(res => {
        this.list = res.rows || []
        this.currentData = this.list.length > 0 ? this.list[0] : {}
        this.loading = false
      }).catch(err => {
        console.error('获取营养干预方案失败:', err)
        this.loading = false
      })
    },
    // 查看PDF报告
    viewPdfReport() {
      console.log('点击查看PDF报告', this.patientId, this.visitRecordId)
      if (!this.patientId || !this.visitRecordId) {
        uni.showToast({
          title: '缺少必要参数',
          icon: 'none'
        })
        return
      }

      // 跳转到PDF预览页面，使用项目统一的跳转方法
      this.$common.navTo(`/pages/patient/pdf-preview?patientId=${this.patientId}&visitId=${this.visitRecordId}`)
    }
  },
}
</script>

<style scoped>
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin: 40rpx 0 20rpx 0;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10rpx;
}

.sub-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 30rpx 0 15rpx 0;
  color: #666;
}

.info-grid, .target-grid, .nutrition-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx 0;
}

.info-item, .target-item, .nutrition-item {
  width: 50%;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #666;
  margin-right: 10rpx;
  min-width: 120rpx;
}

.disease-list {
  margin: 20rpx 0;
}

.disease-item {
  display: inline-block;
  margin: 10rpx 20rpx 10rpx 0;
  padding: 10rpx 20rpx;
  background-color: #e8f5e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #4caf50;
}

.no-disease {
  color: #999;
  font-style: italic;
}

.other-diseases {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.plan-content, .exercise-content {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f0f8ff;
  border-radius: 8rpx;
  border-left: 4px solid #409eff;
  line-height: 1.6;
}

.guidance-content {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-left: 4px solid #409eff;
  border-radius: 8rpx;
}

.diary-content {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #fff8f0;
  border-radius: 8rpx;
}

.diary-item {
  margin-bottom: 15rpx;
  line-height: 1.6;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 100rpx 0;
}

.meal-group, .week-group {
  margin-bottom: 30rpx;
}

.meal-header, .week-header {
  font-size: 30rpx;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 15rpx;
  padding: 10rpx 20rpx;
  background-color: #f0f8ff;
  border-left: 4px solid #409eff;
  border-radius: 4rpx;
}

.report-btn {
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  min-width: 120rpx;
}

.report-btn:hover {
  background-color: #66b1ff;
}
</style>
