{"_from": "@vue/server-renderer@3.4.27", "_id": "@vue/server-renderer@3.4.27", "_inBundle": false, "_integrity": "sha512-dlAMEuvmeA3rJsOMJ2J1kXU7o7pOxgsNHVr9K8hB3ImIkSuBrIdy0vF66h8gf8Tuinf1TK3mPAz2+2sqyf3KzA==", "_location": "/@vue/server-renderer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/server-renderer@3.4.27", "name": "@vue/server-renderer", "escapedName": "@vue%2fserver-renderer", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.4.27.tgz", "_shasum": "3306176f37e648ba665f97dda3ce705687be63d2", "_spec": "@vue/server-renderer@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-ssr": "3.4.27", "@vue/shared": "3.4.27"}, "deprecated": false, "description": "@vue/server-renderer", "exports": {".": {"types": "./dist/server-renderer.d.ts", "node": {"production": "./dist/server-renderer.cjs.prod.js", "development": "./dist/server-renderer.cjs.js", "default": "./index.js"}, "module": "./dist/server-renderer.esm-bundler.js", "import": "./dist/server-renderer.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "name": "@vue/server-renderer", "peerDependencies": {"vue": "3.4.27"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "types": "dist/server-renderer.d.ts", "version": "3.4.27"}