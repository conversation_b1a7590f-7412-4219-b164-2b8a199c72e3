{"_from": "@vue/compiler-dom@3.4.27", "_id": "@vue/compiler-dom@3.4.27", "_inBundle": false, "_integrity": "sha512-kUTvochG/oVgE1w5ViSr3KUBh9X7CWirebA3bezTbB5ZKBQZwR2Mwj9uoSKRMFcz4gSMzzLXBPD6KpCLb9nvWw==", "_location": "/@vue/compiler-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-dom@3.4.27", "name": "@vue/compiler-dom", "escapedName": "@vue%2fcompiler-dom", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/", "/@vue/compiler-sfc", "/@vue/compiler-ssr"], "_resolved": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.4.27.tgz", "_shasum": "d51d35f40d00ce235d7afc6ad8b09dfd92b1cc1c", "_spec": "@vue/compiler-dom@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerDOM", "compat": true, "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-core": "3.4.27", "@vue/shared": "3.4.27"}, "deprecated": false, "description": "@vue/compiler-dom", "exports": {".": {"types": "./dist/compiler-dom.d.ts", "node": {"production": "./dist/compiler-dom.cjs.prod.js", "development": "./dist/compiler-dom.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-dom.esm-bundler.js", "import": "./dist/compiler-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-dom#readme", "jsdelivr": "dist/compiler-dom.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-dom.esm-bundler.js", "name": "@vue/compiler-dom", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-dom"}, "sideEffects": false, "types": "dist/compiler-dom.d.ts", "unpkg": "dist/compiler-dom.global.js", "version": "3.4.27"}