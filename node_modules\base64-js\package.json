{"_from": "base64-js@^1.3.1", "_id": "base64-js@1.5.1", "_inBundle": false, "_integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "_location": "/base64-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "base64-js@^1.3.1", "name": "base64-js", "escapedName": "base64-js", "rawSpec": "^1.3.1", "saveSpec": null, "fetchSpec": "^1.3.1"}, "_requiredBy": ["/sm4js"], "_resolved": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "_shasum": "1b1b440160a5bf7ad40b650f095963481903930a", "_spec": "base64-js@^1.3.1", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp\\node_modules\\sm4js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Base64 encoding/decoding in pure JS", "devDependencies": {"babel-minify": "^0.5.1", "benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "name": "base64-js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | minify > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "typings": "index.d.ts", "version": "1.5.1"}