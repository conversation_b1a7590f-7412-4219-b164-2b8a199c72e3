{"_from": "picocolors@1.0.1", "_id": "picocolors@1.0.1", "_inBundle": false, "_integrity": "sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==", "_location": "/picocolors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "picocolors@1.0.1", "name": "picocolors", "escapedName": "picocolors", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/", "/postcss"], "_resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.1.tgz", "_shasum": "a8ad579b571952f0e5d25892de5445bcfe25aaa1", "_spec": "picocolors@1.0.1", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON><PERSON>"}, "browser": {"./picocolors.js": "./picocolors.browser.js"}, "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "files": ["picocolors.*", "types.ts"], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "license": "ISC", "main": "./picocolors.js", "name": "picocolors", "repository": {"type": "git", "url": "git+https://github.com/alexeyraspopov/picocolors.git"}, "sideEffects": false, "types": "./picocolors.d.ts", "version": "1.0.1"}