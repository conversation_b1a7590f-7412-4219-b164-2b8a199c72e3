{"_from": "@vue/compiler-ssr@3.4.27", "_id": "@vue/compiler-ssr@3.4.27", "_inBundle": false, "_integrity": "sha512-CVRzSJ<PERSON>ltzMG5FcidsW0jKNQnNRYC8bT21VegyMMtHmhW3UOI7knmUehzswXLrExDLE6lQCZdrhD4ogI7c+vuw==", "_location": "/@vue/compiler-ssr", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-ssr@3.4.27", "name": "@vue/compiler-ssr", "escapedName": "@vue%2fcompiler-ssr", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/", "/@vue/compiler-sfc", "/@vue/server-renderer"], "_resolved": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.4.27.tgz", "_shasum": "2a8ecfef1cf448b09be633901a9c020360472e3d", "_spec": "@vue/compiler-ssr@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"prod": false, "formats": ["cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-dom": "3.4.27", "@vue/shared": "3.4.27"}, "deprecated": false, "description": "@vue/compiler-ssr", "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-ssr#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-ssr.cjs.js", "name": "@vue/compiler-ssr", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-ssr"}, "types": "dist/compiler-ssr.d.ts", "version": "3.4.27"}