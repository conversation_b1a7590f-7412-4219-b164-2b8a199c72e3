{"_from": "@vue/runtime-core@3.4.27", "_id": "@vue/runtime-core@3.4.27", "_inBundle": false, "_integrity": "sha512-7aYA9GEbOOdviqVvcuweTLe5Za4qBZkUY7SvET6vE8kyypxVgaT1ixHLg4urtOlrApdgcdgHoTZCUuTGap/5WA==", "_location": "/@vue/runtime-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/runtime-core@3.4.27", "name": "@vue/runtime-core", "escapedName": "@vue%2fruntime-core", "scope": "@vue", "rawSpec": "3.4.27", "saveSpec": null, "fetchSpec": "3.4.27"}, "_requiredBy": ["/", "/@vue/runtime-dom"], "_resolved": "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.4.27.tgz", "_shasum": "1b6e1d71e4604ba7442dd25ed22e4a1fc6adbbda", "_spec": "@vue/runtime-core@3.4.27", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueRuntimeCore", "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/reactivity": "3.4.27", "@vue/shared": "3.4.27"}, "deprecated": false, "description": "@vue/runtime-core", "exports": {".": {"types": "./dist/runtime-core.d.ts", "node": {"production": "./dist/runtime-core.cjs.prod.js", "development": "./dist/runtime-core.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-core.esm-bundler.js", "import": "./dist/runtime-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/runtime-core.esm-bundler.js", "name": "@vue/runtime-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-core"}, "sideEffects": false, "types": "dist/runtime-core.d.ts", "version": "3.4.27"}