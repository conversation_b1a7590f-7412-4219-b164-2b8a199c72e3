/**
* @vue/compiler-dom v3.4.27
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
var VueCompilerDOM=function(e){"use strict";
/*! #__NO_SIDE_EFFECTS__ */function t(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const n={},s=()=>{},i=()=>!1,o=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),r=Object.assign,a=Array.isArray,c=e=>"string"==typeof e,l=e=>"symbol"==typeof e,h=e=>null!==e&&"object"==typeof e,p=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),d=t("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),u=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},f=/-(\w)/g,m=u((e=>e.replace(f,((e,t)=>t?t.toUpperCase():"")))),E=u((e=>e.charAt(0).toUpperCase()+e.slice(1))),_=u((e=>e?`on${E(e)}`:""));const S=/;(?![^(]*\))/g,T=/:([^]+)/,g=/\/\*[^]*?\*\//g;const N=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),I=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),y=t("annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics"),O=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),A=Symbol(""),C=Symbol(""),b=Symbol(""),v=Symbol(""),R=Symbol(""),x=Symbol(""),L=Symbol(""),M=Symbol(""),P=Symbol(""),D=Symbol(""),k=Symbol(""),V=Symbol(""),X=Symbol(""),w=Symbol(""),U=Symbol(""),F=Symbol(""),B=Symbol(""),$=Symbol(""),H=Symbol(""),G=Symbol(""),q=Symbol(""),J=Symbol(""),j=Symbol(""),W=Symbol(""),K=Symbol(""),Y=Symbol(""),Q=Symbol(""),z=Symbol(""),Z=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),se=Symbol(""),ie=Symbol(""),oe=Symbol(""),re=Symbol(""),ae=Symbol(""),ce=Symbol(""),le=Symbol(""),he={[A]:"Fragment",[C]:"Teleport",[b]:"Suspense",[v]:"KeepAlive",[R]:"BaseTransition",[x]:"openBlock",[L]:"createBlock",[M]:"createElementBlock",[P]:"createVNode",[D]:"createElementVNode",[k]:"createCommentVNode",[V]:"createTextVNode",[X]:"createStaticVNode",[w]:"resolveComponent",[U]:"resolveDynamicComponent",[F]:"resolveDirective",[B]:"resolveFilter",[$]:"withDirectives",[H]:"renderList",[G]:"renderSlot",[q]:"createSlots",[J]:"toDisplayString",[j]:"mergeProps",[W]:"normalizeClass",[K]:"normalizeStyle",[Y]:"normalizeProps",[Q]:"guardReactiveProps",[z]:"toHandlers",[Z]:"camelize",[ee]:"capitalize",[te]:"toHandlerKey",[ne]:"setBlockTracking",[se]:"pushScopeId",[ie]:"popScopeId",[oe]:"withCtx",[re]:"unref",[ae]:"isRef",[ce]:"withMemo",[le]:"isMemoSame"};function pe(e){Object.getOwnPropertySymbols(e).forEach((t=>{he[t]=e[t]}))}const de={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function ue(e,t=""){return{type:0,source:t,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:de}}function fe(e,t,n,s,i,o,r,a=!1,c=!1,l=!1,h=de){return e&&(a?(e.helper(x),e.helper(Ce(e.inSSR,l))):e.helper(Ae(e.inSSR,l)),r&&e.helper($)),{type:13,tag:t,props:n,children:s,patchFlag:i,dynamicProps:o,directives:r,isBlock:a,disableTracking:c,isComponent:l,loc:h}}function me(e,t=de){return{type:17,loc:t,elements:e}}function Ee(e,t=de){return{type:15,loc:t,properties:e}}function _e(e,t){return{type:16,loc:de,key:c(e)?Se(e,!0):e,value:t}}function Se(e,t=!1,n=de,s=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:s}}function Te(e,t=de){return{type:8,loc:t,children:e}}function ge(e,t=[],n=de){return{type:14,loc:n,callee:e,arguments:t}}function Ne(e,t=void 0,n=!1,s=!1,i=de){return{type:18,params:e,returns:t,newline:n,isSlot:s,loc:i}}function Ie(e,t,n,s=!0){return{type:19,test:e,consequent:t,alternate:n,newline:s,loc:de}}function ye(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:de}}function Oe(e){return{type:21,body:e,loc:de}}function Ae(e,t){return e||t?P:D}function Ce(e,t){return e||t?L:M}function be(e,{helper:t,removeHelper:n,inSSR:s}){e.isBlock||(e.isBlock=!0,n(Ae(s,e.isComponent)),t(x),t(Ce(s,e.isComponent)))}const ve=new Uint8Array([123,123]),Re=new Uint8Array([125,125]);function xe(e){return e>=97&&e<=122||e>=65&&e<=90}function Le(e){return 32===e||10===e||9===e||12===e||13===e}function Me(e){return 47===e||62===e||Le(e)}function Pe(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}const De={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])};const ke={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTERS:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function Ve(e,{compatConfig:t}){const n=t&&t[e];return"MODE"===e?n||3:n}function Xe(e,t){const n=Ve("MODE",t),s=Ve(e,t);return 3===n?!0===s:!1!==s}function we(e,t,n,...s){return Xe(e,t)}function Ue(e){throw e}function Fe(e){}function Be(e,t,n,s){const i=new SyntaxError(String(`https://vuejs.org/error-reference/#compiler-${e}`));return i.code=e,i.loc=t,i}const $e={0:"Illegal comment.",1:"CDATA section is allowed only in XML context.",2:"Duplicate attribute.",3:"End tag cannot have attributes.",4:"Illegal '/' in tags.",5:"Unexpected EOF in tag.",6:"Unexpected EOF in CDATA section.",7:"Unexpected EOF in comment.",8:"Unexpected EOF in script.",9:"Unexpected EOF in tag.",10:"Incorrectly closed comment.",11:"Incorrectly opened comment.",12:"Illegal tag name. Use '&lt;' to print '<'.",13:"Attribute value was expected.",14:"End tag name was expected.",15:"Whitespace was expected.",16:"Unexpected '\x3c!--' in comment.",17:"Attribute name cannot contain U+0022 (\"), U+0027 ('), and U+003C (<).",18:"Unquoted attribute value cannot contain U+0022 (\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).",19:"Attribute name cannot start with '='.",21:"'<?' is allowed only in XML context.",20:"Unexpected null character.",22:"Illegal '/' in tags.",23:"Invalid end tag.",24:"Element is missing end tag.",25:"Interpolation end sign was not found.",27:"End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.",26:"Legal directive name was expected.",28:"v-if/v-else-if is missing expression.",29:"v-if/else branches must use unique keys.",30:"v-else/v-else-if has no adjacent v-if or v-else-if.",31:"v-for is missing expression.",32:"v-for has invalid expression.",33:"<template v-for> key should be placed on the <template> tag.",34:"v-bind is missing expression.",52:"v-bind with same-name shorthand only allows static argument.",35:"v-on is missing expression.",36:"Unexpected custom directive on <slot> outlet.",37:"Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.",38:"Duplicate slot names found. ",39:"Extraneous children found when component already has explicitly named default slot. These children will be ignored.",40:"v-slot can only be used on components or <template> tags.",41:"v-model is missing expression.",42:"v-model value must be a valid JavaScript member expression.",43:"v-model cannot be used on v-for or v-slot scope variables because they are not writable.",44:"v-model cannot be used on a prop, because local prop bindings are not writable.\nUse a v-bind binding combined with a v-on listener that emits update:x event instead.",45:"Error parsing JavaScript expression: ",46:"<KeepAlive> expects exactly one child component.",51:"@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.",47:'"prefixIdentifiers" option is not supported in this build of compiler.',48:"ES module mode is not supported in this build of compiler.",49:'"cacheHandlers" option is only supported when the "prefixIdentifiers" option is enabled.',50:'"scopeId" option is only supported in module mode.',53:""};function He(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(const s of e.properties)He("RestElement"===s.type?s.argument:s.value,t);break;case"ArrayPattern":e.elements.forEach((e=>{e&&He(e,t)}));break;case"RestElement":He(e.argument,t);break;case"AssignmentPattern":He(e.left,t)}return t}const Ge=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed,qe=["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"];const Je=e=>4===e.type&&e.isStatic;function je(e){switch(e){case"Teleport":case"teleport":return C;case"Suspense":case"suspense":return b;case"KeepAlive":case"keep-alive":return v;case"BaseTransition":case"base-transition":return R}}const We=/^\d|[^\$\w]/,Ke=e=>!We.test(e),Ye=/[A-Za-z_$\xA0-\uFFFF]/,Qe=/[\.\?\w$\xA0-\uFFFF]/,ze=/\s+[.[]\s*|\s*[.[]\s+/g,Ze=e=>{e=e.trim().replace(ze,(e=>e.trim()));let t=0,n=[],s=0,i=0,o=null;for(let r=0;r<e.length;r++){const a=e.charAt(r);switch(t){case 0:if("["===a)n.push(t),t=1,s++;else if("("===a)n.push(t),t=2,i++;else if(!(0===r?Ye:Qe).test(a))return!1;break;case 1:"'"===a||'"'===a||"`"===a?(n.push(t),t=3,o=a):"["===a?s++:"]"===a&&(--s||(t=n.pop()));break;case 2:if("'"===a||'"'===a||"`"===a)n.push(t),t=3,o=a;else if("("===a)i++;else if(")"===a){if(r===e.length-1)return!1;--i||(t=n.pop())}break;case 3:a===o&&(t=n.pop(),o=null)}}return!s&&!i},et=s,tt=Ze;function nt(e,t,n=t.length){let s=0,i=-1;for(let o=0;o<n;o++)10===t.charCodeAt(o)&&(s++,i=o);return e.offset+=n,e.line+=s,e.column=-1===i?e.column+n:n-i,e}function st(e,t,n=!1){for(let s=0;s<e.props.length;s++){const i=e.props[s];if(7===i.type&&(n||i.exp)&&(c(t)?i.name===t:t.test(i.name)))return i}}function it(e,t,n=!1,s=!1){for(let i=0;i<e.props.length;i++){const o=e.props[i];if(6===o.type){if(n)continue;if(o.name===t&&(o.value||s))return o}else if("bind"===o.name&&(o.exp||s)&&ot(o.arg,t))return o}}function ot(e,t){return!(!e||!Je(e)||e.content!==t)}function rt(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function at(e){return 5===e.type||2===e.type}function ct(e){return 7===e.type&&"slot"===e.name}function lt(e){return 1===e.type&&3===e.tagType}function ht(e){return 1===e.type&&2===e.tagType}const pt=new Set([Y,Q]);function dt(e,t=[]){if(e&&!c(e)&&14===e.type){const n=e.callee;if(!c(n)&&pt.has(n))return dt(e.arguments[0],t.concat(e))}return[e,t]}function ut(e,t,n){let s,i,o=13===e.type?e.props:e.arguments[2],r=[];if(o&&!c(o)&&14===o.type){const e=dt(o);o=e[0],r=e[1],i=r[r.length-1]}if(null==o||c(o))s=Ee([t]);else if(14===o.type){const e=o.arguments[0];c(e)||15!==e.type?o.callee===z?s=ge(n.helper(j),[Ee([t]),o]):o.arguments.unshift(Ee([t])):ft(t,e)||e.properties.unshift(t),!s&&(s=o)}else 15===o.type?(ft(t,o)||o.properties.unshift(t),s=o):(s=ge(n.helper(j),[Ee([t]),o]),i&&i.callee===Q&&(i=r[r.length-2]));13===e.type?i?i.arguments[0]=s:e.props=s:i?i.arguments[0]=s:e.arguments[2]=s}function ft(e,t){let n=!1;if(4===e.key.type){const s=e.key.content;n=t.properties.some((e=>4===e.key.type&&e.key.content===s))}return n}function mt(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}function Et(e){return 14===e.type&&e.callee===ce?e.arguments[1].returns:e}const _t=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,St={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:i,isPreTag:i,isCustomElement:i,onError:Ue,onWarn:Fe,comments:!1,prefixIdentifiers:!1};let Tt=St,gt=null,Nt="",It=null,yt=null,Ot="",At=-1,Ct=-1,bt=0,vt=!1,Rt=null;const xt=[],Lt=new class{constructor(e,t){this.stack=e,this.cbs=t,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=ve,this.delimiterClose=Re,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return 2===this.mode&&0===this.stack.length}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=ve,this.delimiterClose=Re}getPos(e){let t=1,n=e+1;for(let s=this.newlines.length-1;s>=0;s--){const i=this.newlines[s];if(e>i){t=s+2,n=e-i;break}}return{column:n,line:t,offset:e}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(e){60===e?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e))}stateInterpolationOpen(e){if(e===this.delimiterOpen[this.delimiterIndex])if(this.delimiterIndex===this.delimiterOpen.length-1){const e=this.index+1-this.delimiterOpen.length;e>this.sectionStart&&this.cbs.ontext(this.sectionStart,e),this.state=3,this.sectionStart=e}else this.delimiterIndex++;else this.inRCDATA?(this.state=32,this.stateInRCDATA(e)):(this.state=1,this.stateText(e))}stateInterpolation(e){e===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(e))}stateInterpolationClose(e){e===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.state=this.inRCDATA?32:1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(e))}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?Me(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.inRCDATA=!1;this.sequenceIndex=0,this.state=6,this.stateInTagName(e)}stateInRCDATA(e){if(this.sequenceIndex===this.currentSequence.length){if(62===e||Le(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.sectionStart=t+2,this.stateInClosingTagName(e),void(this.inRCDATA=!1)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===De.TitleEnd||this.currentSequence===De.TextareaEnd&&!this.inSFCRoot?e===this.delimiterOpen[0]&&(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e)):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=Number(60===e)}stateCDATASequence(e){e===De.Cdata[this.sequenceIndex]?++this.sequenceIndex===De.Cdata.length&&(this.state=28,this.currentSequence=De.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length;){const t=this.buffer.charCodeAt(this.index);if(10===t&&this.newlines.push(this.index),t===e)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===De.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(e,t){this.enterRCDATA(e,t),this.state=31}enterRCDATA(e,t){this.inRCDATA=!0,this.currentSequence=e,this.sequenceIndex=t}stateBeforeTagName(e){33===e?(this.state=22,this.sectionStart=this.index+1):63===e?(this.state=24,this.sectionStart=this.index+1):xe(e)?(this.sectionStart=this.index,this.state=0===this.mode?6:this.inSFCRoot?34:this.inXML?6:116===e?30:115===e?29:6):47===e?this.state=8:(this.state=1,this.stateText(e))}stateInTagName(e){Me(e)&&this.handleTagName(e)}stateInSFCRootTagName(e){if(Me(e)){const t=this.buffer.slice(this.sectionStart,this.index);"template"!==t&&this.enterRCDATA(Pe("</"+t),0),this.handleTagName(e)}}handleTagName(e){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)}stateBeforeClosingTagName(e){Le(e)||(62===e?(this.state=1,this.sectionStart=this.index+1):(this.state=xe(e)?9:27,this.sectionStart=this.index))}stateInClosingTagName(e){(62===e||Le(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){62===e&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(e){62===e?(this.cbs.onopentagend(this.index),this.state=this.inRCDATA?32:1,this.sectionStart=this.index+1):47===e?this.state=7:60===e&&47===this.peek()?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):Le(e)||this.handleAttrStart(e)}handleAttrStart(e){118===e&&45===this.peek()?(this.state=13,this.sectionStart=this.index):46===e||58===e||64===e||35===e?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(e){62===e?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):Le(e)||(this.state=11,this.stateBeforeAttrName(e))}stateInAttrName(e){(61===e||Me(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(e))}stateInDirName(e){61===e||Me(e)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(e)):58===e?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):46===e&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(e){61===e||Me(e)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(e)):91===e?this.state=15:46===e&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(e){93===e?this.state=14:(61===e||Me(e))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(e))}stateInDirModifier(e){61===e||Me(e)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(e)):46===e&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(e){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(e)}stateAfterAttrName(e){61===e?this.state=18:47===e||62===e?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)):Le(e)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(e))}stateBeforeAttrValue(e){34===e?(this.state=19,this.sectionStart=this.index+1):39===e?(this.state=20,this.sectionStart=this.index+1):Le(e)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(e))}handleInAttrValue(e,t){(e===t||this.fastForwardTo(t))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(34===t?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(e){this.handleInAttrValue(e,34)}stateInAttrValueSingleQuotes(e){this.handleInAttrValue(e,39)}stateInAttrValueNoQuotes(e){Le(e)||62===e?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(e)):39!==e&&60!==e&&61!==e&&96!==e||this.cbs.onerr(18,this.index)}stateBeforeDeclaration(e){91===e?(this.state=26,this.sequenceIndex=0):this.state=45===e?25:23}stateInDeclaration(e){(62===e||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(62===e||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(e){45===e?(this.state=28,this.currentSequence=De.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(e){(62===e||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){e===De.ScriptEnd[3]?this.startSpecial(De.ScriptEnd,4):e===De.StyleEnd[3]?this.startSpecial(De.StyleEnd,4):(this.state=6,this.stateInTagName(e))}stateBeforeSpecialT(e){e===De.TitleEnd[3]?this.startSpecial(De.TitleEnd,4):e===De.TextareaEnd[3]?this.startSpecial(De.TextareaEnd,4):(this.state=6,this.stateInTagName(e))}startEntity(){}stateInEntity(){}parse(e){for(this.buffer=e;this.index<this.buffer.length;){const e=this.buffer.charCodeAt(this.index);switch(10===e&&this.newlines.push(this.index),this.state){case 1:this.stateText(e);break;case 2:this.stateInterpolationOpen(e);break;case 3:this.stateInterpolation(e);break;case 4:this.stateInterpolationClose(e);break;case 31:this.stateSpecialStartSequence(e);break;case 32:this.stateInRCDATA(e);break;case 26:this.stateCDATASequence(e);break;case 19:this.stateInAttrValueDoubleQuotes(e);break;case 12:this.stateInAttrName(e);break;case 13:this.stateInDirName(e);break;case 14:this.stateInDirArg(e);break;case 15:this.stateInDynamicDirArg(e);break;case 16:this.stateInDirModifier(e);break;case 28:this.stateInCommentLike(e);break;case 27:this.stateInSpecialComment(e);break;case 11:this.stateBeforeAttrName(e);break;case 6:this.stateInTagName(e);break;case 34:this.stateInSFCRootTagName(e);break;case 9:this.stateInClosingTagName(e);break;case 5:this.stateBeforeTagName(e);break;case 17:this.stateAfterAttrName(e);break;case 20:this.stateInAttrValueSingleQuotes(e);break;case 18:this.stateBeforeAttrValue(e);break;case 8:this.stateBeforeClosingTagName(e);break;case 10:this.stateAfterClosingTagName(e);break;case 29:this.stateBeforeSpecialS(e);break;case 30:this.stateBeforeSpecialT(e);break;case 21:this.stateInAttrValueNoQuotes(e);break;case 7:this.stateInSelfClosingTag(e);break;case 23:this.stateInDeclaration(e);break;case 22:this.stateBeforeDeclaration(e);break;case 25:this.stateBeforeComment(e);break;case 24:this.stateInProcessingInstruction(e);break;case 33:this.stateInEntity()}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(1===this.state||32===this.state&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):19!==this.state&&20!==this.state&&21!==this.state||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length;this.sectionStart>=e||(28===this.state?this.currentSequence===De.CdataEnd?this.cbs.oncdata(this.sectionStart,e):this.cbs.oncomment(this.sectionStart,e):6===this.state||11===this.state||18===this.state||17===this.state||12===this.state||13===this.state||14===this.state||15===this.state||16===this.state||20===this.state||19===this.state||21===this.state||9===this.state||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){}}(xt,{onerr:Qt,ontext(e,t){Vt(Dt(e,t),e,t)},ontextentity(e,t,n){Vt(e,t,n)},oninterpolation(e,t){if(vt)return Vt(Dt(e,t),e,t);let n=e+Lt.delimiterOpen.length,s=t-Lt.delimiterClose.length;for(;Le(Nt.charCodeAt(n));)n++;for(;Le(Nt.charCodeAt(s-1));)s--;let i=Dt(n,s);i.includes("&")&&(i=Tt.decodeEntities(i,!1)),Jt({type:5,content:Yt(i,!1,jt(n,s)),loc:jt(e,t)})},onopentagname(e,t){const n=Dt(e,t);It={type:1,tag:n,ns:Tt.getNamespace(n,xt[0],Tt.ns),tagType:0,props:[],children:[],loc:jt(e-1,t),codegenNode:void 0}},onopentagend(e){kt(e)},onclosetag(e,t){const n=Dt(e,t);if(!Tt.isVoidTag(n)){let s=!1;for(let e=0;e<xt.length;e++){if(xt[e].tag.toLowerCase()===n.toLowerCase()){s=!0;for(let n=0;n<=e;n++){Xt(xt.shift(),t,n<e)}break}}s||wt(e,60)}},onselfclosingtag(e){const t=It.tag;It.isSelfClosing=!0,kt(e),xt[0]&&xt[0].tag===t&&Xt(xt.shift(),e)},onattribname(e,t){yt={type:6,name:Dt(e,t),nameLoc:jt(e,t),value:void 0,loc:jt(e)}},ondirname(e,t){const n=Dt(e,t),s="."===n||":"===n?"bind":"@"===n?"on":"#"===n?"slot":n.slice(2);if(vt||""===s)yt={type:6,name:n,nameLoc:jt(e,t),value:void 0,loc:jt(e)};else if(yt={type:7,name:s,rawName:n,exp:void 0,arg:void 0,modifiers:"."===n?["prop"]:[],loc:jt(e)},"pre"===s){vt=Lt.inVPre=!0,Rt=It;const e=It.props;for(let t=0;t<e.length;t++)7===e[t].type&&(e[t]=Kt(e[t]))}},ondirarg(e,t){if(e===t)return;const n=Dt(e,t);if(vt)yt.name+=n,Wt(yt.nameLoc,t);else{const s="["!==n[0];yt.arg=Yt(s?n:n.slice(1,-1),s,jt(e,t),s?3:0)}},ondirmodifier(e,t){const n=Dt(e,t);if(vt)yt.name+="."+n,Wt(yt.nameLoc,t);else if("slot"===yt.name){const e=yt.arg;e&&(e.content+="."+n,Wt(e.loc,t))}else yt.modifiers.push(n)},onattribdata(e,t){Ot+=Dt(e,t),At<0&&(At=e),Ct=t},onattribentity(e,t,n){Ot+=e,At<0&&(At=t),Ct=n},onattribnameend(e){const t=Dt(yt.loc.start.offset,e);7===yt.type&&(yt.rawName=t),It.props.some((e=>(7===e.type?e.rawName:e.name)===t))},onattribend(e,t){if(It&&yt){if(Wt(yt.loc,t),0!==e)if(Ot.includes("&")&&(Ot=Tt.decodeEntities(Ot,!0)),6===yt.type)"class"===yt.name&&(Ot=qt(Ot).trim()),yt.value={type:2,content:Ot,loc:1===e?jt(At,Ct):jt(At-1,Ct+1)},Lt.inSFCRoot&&"template"===It.tag&&"lang"===yt.name&&Ot&&"html"!==Ot&&Lt.enterRCDATA(Pe("</template"),0);else{let e=0;yt.exp=Yt(Ot,!1,jt(At,Ct),0,e),"for"===yt.name&&(yt.forParseResult=function(e){const t=e.loc,n=e.content,s=n.match(_t);if(!s)return;const[,i,o]=s,r=(e,n,s=!1)=>{const i=t.start.offset+n;return Yt(e,!1,jt(i,i+e.length),0,s?1:0)},a={source:r(o.trim(),n.indexOf(o,i.length)),value:void 0,key:void 0,index:void 0,finalized:!1};let c=i.trim().replace(Pt,"").trim();const l=i.indexOf(c),h=c.match(Mt);if(h){c=c.replace(Mt,"").trim();const e=h[1].trim();let t;if(e&&(t=n.indexOf(e,l+c.length),a.key=r(e,t,!0)),h[2]){const s=h[2].trim();s&&(a.index=r(s,n.indexOf(s,a.key?t+e.length:l+c.length),!0))}}c&&(a.value=r(c,l,!0));return a}(yt.exp));let t=-1;"bind"===yt.name&&(t=yt.modifiers.indexOf("sync"))>-1&&we("COMPILER_V_BIND_SYNC",Tt,0)&&(yt.name="model",yt.modifiers.splice(t,1))}7===yt.type&&"pre"===yt.name||It.props.push(yt)}Ot="",At=Ct=-1},oncomment(e,t){Tt.comments&&Jt({type:3,content:Dt(e,t),loc:jt(e-4,t+3)})},onend(){const e=Nt.length;for(let t=0;t<xt.length;t++)Xt(xt[t],e-1)},oncdata(e,t){0!==xt[0].ns&&Vt(Dt(e,t),e,t)},onprocessinginstruction(e){0===(xt[0]?xt[0].ns:Tt.ns)&&Qt(21,e-1)}}),Mt=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Pt=/^\(|\)$/g;function Dt(e,t){return Nt.slice(e,t)}function kt(e){Lt.inSFCRoot&&(It.innerLoc=jt(e+1,e+1)),Jt(It);const{tag:t,ns:n}=It;0===n&&Tt.isPreTag(t)&&bt++,Tt.isVoidTag(t)?Xt(It,e):(xt.unshift(It),1!==n&&2!==n||(Lt.inXML=!0)),It=null}function Vt(e,t,n){{const t=xt[0]&&xt[0].tag;"script"!==t&&"style"!==t&&e.includes("&")&&(e=Tt.decodeEntities(e,!1))}const s=xt[0]||gt,i=s.children[s.children.length-1];i&&2===i.type?(i.content+=e,Wt(i.loc,n)):s.children.push({type:2,content:e,loc:jt(t,n)})}function Xt(e,t,n=!1){Wt(e.loc,n?wt(t,60):function(e,t){let n=e;for(;Nt.charCodeAt(n)!==t&&n<Nt.length-1;)n++;return n}(t,62)+1),Lt.inSFCRoot&&(e.innerLoc.end=r({},e.children.length?e.children[e.children.length-1].loc.end:e.innerLoc.start),e.innerLoc.source=Dt(e.innerLoc.start.offset,e.innerLoc.end.offset));const{tag:s,ns:i}=e;vt||("slot"===s?e.tagType=2:Ft(e)?e.tagType=3:function({tag:e,props:t}){if(Tt.isCustomElement(e))return!1;if("component"===e||(n=e.charCodeAt(0),n>64&&n<91)||je(e)||Tt.isBuiltInComponent&&Tt.isBuiltInComponent(e)||Tt.isNativeTag&&!Tt.isNativeTag(e))return!0;var n;for(let s=0;s<t.length;s++){const e=t[s];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(we("COMPILER_IS_ON_ELEMENT",Tt))return!0}}else if("bind"===e.name&&ot(e.arg,"is")&&we("COMPILER_IS_ON_ELEMENT",Tt))return!0}return!1}(e)&&(e.tagType=1)),Lt.inRCDATA||(e.children=$t(e.children,e.tag)),0===i&&Tt.isPreTag(s)&&bt--,Rt===e&&(vt=Lt.inVPre=!1,Rt=null),Lt.inXML&&0===(xt[0]?xt[0].ns:Tt.ns)&&(Lt.inXML=!1);{const t=e.props;if(!Lt.inSFCRoot&&Xe("COMPILER_NATIVE_TEMPLATE",Tt)&&"template"===e.tag&&!Ft(e)){const t=xt[0]||gt,n=t.children.indexOf(e);t.children.splice(n,1,...e.children)}const n=t.find((e=>6===e.type&&"inline-template"===e.name));n&&we("COMPILER_INLINE_TEMPLATE",Tt)&&e.children.length&&(n.value={type:2,content:Dt(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:n.loc})}}function wt(e,t){let n=e;for(;Nt.charCodeAt(n)!==t&&n>=0;)n--;return n}const Ut=new Set(["if","else","else-if","for","slot"]);function Ft({tag:e,props:t}){if("template"===e)for(let n=0;n<t.length;n++)if(7===t[n].type&&Ut.has(t[n].name))return!0;return!1}const Bt=/\r\n/g;function $t(e,t){const n="preserve"!==Tt.whitespace;let s=!1;for(let i=0;i<e.length;i++){const t=e[i];if(2===t.type)if(bt)t.content=t.content.replace(Bt,"\n");else if(Ht(t.content)){const o=e[i-1]&&e[i-1].type,r=e[i+1]&&e[i+1].type;!o||!r||n&&(3===o&&(3===r||1===r)||1===o&&(3===r||1===r&&Gt(t.content)))?(s=!0,e[i]=null):t.content=" "}else n&&(t.content=qt(t.content))}if(bt&&t&&Tt.isPreTag(t)){const t=e[0];t&&2===t.type&&(t.content=t.content.replace(/^\r?\n/,""))}return s?e.filter(Boolean):e}function Ht(e){for(let t=0;t<e.length;t++)if(!Le(e.charCodeAt(t)))return!1;return!0}function Gt(e){for(let t=0;t<e.length;t++){const n=e.charCodeAt(t);if(10===n||13===n)return!0}return!1}function qt(e){let t="",n=!1;for(let s=0;s<e.length;s++)Le(e.charCodeAt(s))?n||(t+=" ",n=!0):(t+=e[s],n=!1);return t}function Jt(e){(xt[0]||gt).children.push(e)}function jt(e,t){return{start:Lt.getPos(e),end:null==t?t:Lt.getPos(t),source:null==t?t:Dt(e,t)}}function Wt(e,t){e.end=Lt.getPos(t),e.source=Dt(e.start.offset,t)}function Kt(e){const t={type:6,name:e.rawName,nameLoc:jt(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){const n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}function Yt(e,t=!1,n,s=0,i=0){return Se(e,t,n,s)}function Qt(e,t,n){Tt.onError(Be(e,jt(t,t)))}function zt(e,t){if(Lt.reset(),It=null,yt=null,Ot="",At=-1,Ct=-1,xt.length=0,Nt=e,Tt=r({},St),t){let e;for(e in t)null!=t[e]&&(Tt[e]=t[e])}Lt.mode="html"===Tt.parseMode?1:"sfc"===Tt.parseMode?2:0,Lt.inXML=1===Tt.ns||2===Tt.ns;const n=t&&t.delimiters;n&&(Lt.delimiterOpen=Pe(n[0]),Lt.delimiterClose=Pe(n[1]));const s=gt=ue([],e);return Lt.parse(Nt),s.loc=jt(0,e.length),s.children=$t(s.children),gt=null,s}function Zt(e,t){tn(e,t,en(e,e.children[0]))}function en(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!ht(t)}function tn(e,t,n=!1){const{children:s}=e,i=s.length;let o=0;for(let r=0;r<s.length;r++){const e=s[r];if(1===e.type&&0===e.tagType){const s=n?0:nn(e,t);if(s>0){if(s>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),o++;continue}}else{const n=e.codegenNode;if(13===n.type){const s=cn(n);if((!s||512===s||1===s)&&rn(e,t)>=2){const s=an(e);s&&(n.props=t.hoist(s))}n.dynamicProps&&(n.dynamicProps=t.hoist(n.dynamicProps))}}}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,tn(e,t),n&&t.scopes.vSlot--}else if(11===e.type)tn(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)tn(e.branches[n],t,1===e.branches[n].children.length)}if(o&&t.transformHoist&&t.transformHoist(s,t,e),o&&o===i&&1===e.type&&0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&a(e.codegenNode.children)){const n=t.hoist(me(e.codegenNode.children));t.hmr&&(n.content=`[...${n.content}]`),e.codegenNode.children=n}}function nn(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const s=n.get(e);if(void 0!==s)return s;const i=e.codegenNode;if(13!==i.type)return 0;if(i.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag)return 0;if(cn(i))return n.set(e,0),0;{let s=3;const o=rn(e,t);if(0===o)return n.set(e,0),0;o<s&&(s=o);for(let i=0;i<e.children.length;i++){const o=nn(e.children[i],t);if(0===o)return n.set(e,0),0;o<s&&(s=o)}if(s>1)for(let i=0;i<e.props.length;i++){const o=e.props[i];if(7===o.type&&"bind"===o.name&&o.exp){const i=nn(o.exp,t);if(0===i)return n.set(e,0),0;i<s&&(s=i)}}if(i.isBlock){for(let t=0;t<e.props.length;t++){if(7===e.props[t].type)return n.set(e,0),0}t.removeHelper(x),t.removeHelper(Ce(t.inSSR,i.isComponent)),i.isBlock=!1,t.helper(Ae(t.inSSR,i.isComponent))}return n.set(e,s),s}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return nn(e.content,t);case 4:return e.constType;case 8:let o=3;for(let n=0;n<e.children.length;n++){const s=e.children[n];if(c(s)||l(s))continue;const i=nn(s,t);if(0===i)return 0;i<o&&(o=i)}return o}}const sn=new Set([W,K,Y,Q]);function on(e,t){if(14===e.type&&!c(e.callee)&&sn.has(e.callee)){const n=e.arguments[0];if(4===n.type)return nn(n,t);if(14===n.type)return on(n,t)}return 0}function rn(e,t){let n=3;const s=an(e);if(s&&15===s.type){const{properties:e}=s;for(let s=0;s<e.length;s++){const{key:i,value:o}=e[s],r=nn(i,t);if(0===r)return r;let a;if(r<n&&(n=r),a=4===o.type?nn(o,t):14===o.type?on(o,t):0,0===a)return a;a<n&&(n=a)}}return n}function an(e){const t=e.codegenNode;if(13===t.type)return t.props}function cn(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function ln(e,{filename:t="",prefixIdentifiers:i=!1,hoistStatic:o=!1,hmr:r=!1,cacheHandlers:a=!1,nodeTransforms:l=[],directiveTransforms:h={},transformHoist:p=null,isBuiltInComponent:d=s,isCustomElement:u=s,expressionPlugins:f=[],scopeId:_=null,slotted:S=!0,ssr:T=!1,inSSR:g=!1,ssrCssVars:N="",bindingMetadata:I=n,inline:y=!1,isTS:O=!1,onError:A=Ue,onWarn:C=Fe,compatConfig:b}){const v=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),R={filename:t,selfName:v&&E(m(v[1])),prefixIdentifiers:i,hoistStatic:o,hmr:r,cacheHandlers:a,nodeTransforms:l,directiveTransforms:h,transformHoist:p,isBuiltInComponent:d,isCustomElement:u,expressionPlugins:f,scopeId:_,slotted:S,ssr:T,inSSR:g,ssrCssVars:N,bindingMetadata:I,inline:y,isTS:O,onError:A,onWarn:C,compatConfig:b,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new WeakMap,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,grandParent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=R.helpers.get(e)||0;return R.helpers.set(e,t+1),e},removeHelper(e){const t=R.helpers.get(e);if(t){const n=t-1;n?R.helpers.set(e,n):R.helpers.delete(e)}},helperString:e=>`_${he[R.helper(e)]}`,replaceNode(e){R.parent.children[R.childIndex]=R.currentNode=e},removeNode(e){const t=e?R.parent.children.indexOf(e):R.currentNode?R.childIndex:-1;e&&e!==R.currentNode?R.childIndex>t&&(R.childIndex--,R.onNodeRemoved()):(R.currentNode=null,R.onNodeRemoved()),R.parent.children.splice(t,1)},onNodeRemoved:s,addIdentifiers(e){},removeIdentifiers(e){},hoist(e){c(e)&&(e=Se(e)),R.hoists.push(e);const t=Se(`_hoisted_${R.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>ye(R.cached++,e,t)};return R.filters=new Set,R}function hn(e,t){const n=ln(e,t);pn(e,n),t.hoistStatic&&Zt(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:s}=e;if(1===s.length){const n=s[0];if(en(e,n)&&n.codegenNode){const s=n.codegenNode;13===s.type&&be(s,t),e.codegenNode=s}else e.codegenNode=n}else if(s.length>1){let s=64;e.codegenNode=fe(t,n(A),void 0,e.children,s+"",void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function pn(e,t){t.currentNode=e;const{nodeTransforms:n}=t,s=[];for(let o=0;o<n.length;o++){const i=n[o](e,t);if(i&&(a(i)?s.push(...i):s.push(i)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(k);break;case 5:t.ssr||t.helper(J);break;case 9:for(let n=0;n<e.branches.length;n++)pn(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const s=()=>{n--};for(;n<e.children.length;n++){const i=e.children[n];c(i)||(t.grandParent=t.parent,t.parent=e,t.childIndex=n,t.onNodeRemoved=s,pn(i,t))}}(e,t)}t.currentNode=e;let i=s.length;for(;i--;)s[i]()}function dn(e,t){const n=c(e)?t=>t===e:t=>e.test(t);return(e,s)=>{if(1===e.type){const{props:i}=e;if(3===e.tagType&&i.some(ct))return;const o=[];for(let r=0;r<i.length;r++){const a=i[r];if(7===a.type&&n(a.name)){i.splice(r,1),r--;const n=t(e,a,s);n&&o.push(n)}}return o}}}const un="/*#__PURE__*/",fn=e=>`${he[e]}: _${he[e]}`;function mn(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:s=!1,filename:i="template.vue.html",scopeId:o=null,optimizeImports:r=!1,runtimeGlobalName:a="Vue",runtimeModuleName:c="vue",ssrRuntimeModuleName:l="vue/server-renderer",ssr:h=!1,isTS:p=!1,inSSR:d=!1}){const u={mode:t,prefixIdentifiers:n,sourceMap:s,filename:i,scopeId:o,optimizeImports:r,runtimeGlobalName:a,runtimeModuleName:c,ssrRuntimeModuleName:l,ssr:h,isTS:p,inSSR:d,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${he[e]}`,push(e,t=-2,n){u.code+=e},indent(){f(++u.indentLevel)},deindent(e=!1){e?--u.indentLevel:f(--u.indentLevel)},newline(){f(u.indentLevel)}};function f(e){u.push("\n"+"  ".repeat(e),0)}return u}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:s,push:i,prefixIdentifiers:o,indent:r,deindent:a,newline:c,ssr:l}=n,h=Array.from(e.helpers),p=h.length>0,d=!o&&"module"!==s;!function(e,t){const{push:n,newline:s,runtimeGlobalName:i}=t,o=i,r=Array.from(e.helpers);if(r.length>0&&(n(`const _Vue = ${o}\n`,-1),e.hoists.length)){n(`const { ${[P,D,k,V,X].filter((e=>r.includes(e))).map(fn).join(", ")} } = _Vue\n`,-1)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:s}=t;s();for(let i=0;i<e.length;i++){const o=e[i];o&&(n(`const _hoisted_${i+1} = `),Tn(o,t),s())}t.pure=!1})(e.hoists,t),s(),n("return ")}(e,n);if(i(`function ${l?"ssrRender":"render"}(${(l?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),r(),d&&(i("with (_ctx) {"),r(),p&&(i(`const { ${h.map(fn).join(", ")} } = _Vue\n`,-1),c())),e.components.length&&(En(e.components,"component",n),(e.directives.length||e.temps>0)&&c()),e.directives.length&&(En(e.directives,"directive",n),e.temps>0&&c()),e.filters&&e.filters.length&&(c(),En(e.filters,"filter",n),c()),e.temps>0){i("let ");for(let t=0;t<e.temps;t++)i(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(i("\n",0),c()),l||i("return "),e.codegenNode?Tn(e.codegenNode,n):i("null"),d&&(a(),i("}")),a(),i("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function En(e,t,{helper:n,push:s,newline:i,isTS:o}){const r=n("filter"===t?B:"component"===t?w:F);for(let a=0;a<e.length;a++){let n=e[a];const c=n.endsWith("__self");c&&(n=n.slice(0,-6)),s(`const ${mt(n,t)} = ${r}(${JSON.stringify(n)}${c?", true":""})${o?"!":""}`),a<e.length-1&&i()}}function _n(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),Sn(e,t,n),n&&t.deindent(),t.push("]")}function Sn(e,t,n=!1,s=!0){const{push:i,newline:o}=t;for(let r=0;r<e.length;r++){const l=e[r];c(l)?i(l,-3):a(l)?_n(l,t):Tn(l,t),r<e.length-1&&(n?(s&&i(","),o()):s&&i(", "))}}function Tn(e,t){if(c(e))t.push(e,-3);else if(l(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:Tn(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),-3,e)}(e,t);break;case 4:gn(e,t);break;case 5:!function(e,t){const{push:n,helper:s,pure:i}=t;i&&n(un);n(`${s(J)}(`),Tn(e.content,t),n(")")}(e,t);break;case 8:Nn(e,t);break;case 3:!function(e,t){const{push:n,helper:s,pure:i}=t;i&&n(un);n(`${s(k)}(${JSON.stringify(e.content)})`,-3,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:s,pure:i}=t,{tag:o,props:r,children:a,patchFlag:c,dynamicProps:l,directives:h,isBlock:p,disableTracking:d,isComponent:u}=e;h&&n(s($)+"(");p&&n(`(${s(x)}(${d?"true":""}), `);i&&n(un);const f=p?Ce(t.inSSR,u):Ae(t.inSSR,u);n(s(f)+"(",-2,e),Sn(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([o,r,a,c,l]),t),n(")"),p&&n(")");h&&(n(", "),Tn(h,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:s,pure:i}=t,o=c(e.callee)?e.callee:s(e.callee);i&&n(un);n(o+"(",-2,e),Sn(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:s,deindent:i,newline:o}=t,{properties:r}=e;if(!r.length)return void n("{}",-2,e);const a=r.length>1||!1;n(a?"{":"{ "),a&&s();for(let c=0;c<r.length;c++){const{key:e,value:s}=r[c];In(e,t),n(": "),Tn(s,t),c<r.length-1&&(n(","),o())}a&&i(),n(a?"}":" }")}(e,t);break;case 17:!function(e,t){_n(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:s,deindent:i}=t,{params:o,returns:r,body:c,newline:l,isSlot:h}=e;h&&n(`_${he[oe]}(`);n("(",-2,e),a(o)?Sn(o,t):o&&Tn(o,t);n(") => "),(l||c)&&(n("{"),s());r?(l&&n("return "),a(r)?_n(r,t):Tn(r,t)):c&&Tn(c,t);(l||c)&&(i(),n("}"));h&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:s,alternate:i,newline:o}=e,{push:r,indent:a,deindent:c,newline:l}=t;if(4===n.type){const e=!Ke(n.content);e&&r("("),gn(n,t),e&&r(")")}else r("("),Tn(n,t),r(")");o&&a(),t.indentLevel++,o||r(" "),r("? "),Tn(s,t),t.indentLevel--,o&&l(),o||r(" "),r(": ");const h=19===i.type;h||t.indentLevel++;Tn(i,t),h||t.indentLevel--;o&&c(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:s,indent:i,deindent:o,newline:r}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(i(),n(`${s(ne)}(-1),`),r());n(`_cache[${e.index}] = `),Tn(e.value,t),e.isVNode&&(n(","),r(),n(`${s(ne)}(1),`),r(),n(`_cache[${e.index}]`),o());n(")")}(e,t);break;case 21:Sn(e.body,t,!0,!1)}}function gn(e,t){const{content:n,isStatic:s}=e;t.push(s?JSON.stringify(n):n,-3,e)}function Nn(e,t){for(let n=0;n<e.children.length;n++){const s=e.children[n];c(s)?t.push(s,-3):Tn(s,t)}}function In(e,t){const{push:n}=t;if(8===e.type)n("["),Nn(e,t),n("]");else if(e.isStatic){n(Ke(e.content)?e.content:JSON.stringify(e.content),-2,e)}else n(`[${e.content}]`,-3,e)}function yn(e,t,n=!1,s=!1,i=Object.create(t.identifiers)){return e}const On=dn(/^(if|else|else-if)$/,((e,t,n)=>An(e,t,n,((e,t,s)=>{const i=n.parent.children;let o=i.indexOf(e),r=0;for(;o-- >=0;){const e=i[o];e&&9===e.type&&(r+=e.branches.length)}return()=>{if(s)e.codegenNode=bn(t,r,n);else{const s=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);s.alternate=bn(t,r+e.branches.length-1,n)}}}))));function An(e,t,n,s){if(!("else"===t.name||t.exp&&t.exp.content.trim())){const s=t.exp?t.exp.loc:e.loc;n.onError(Be(28,t.loc)),t.exp=Se("true",!1,s)}if("if"===t.name){const i=Cn(e,t),o={type:9,loc:e.loc,branches:[i]};if(n.replaceNode(o),s)return s(o,i,!0)}else{const i=n.parent.children;let o=i.indexOf(e);for(;o-- >=-1;){const r=i[o];if(r&&3===r.type)n.removeNode(r);else{if(!r||2!==r.type||r.content.trim().length){if(r&&9===r.type){"else-if"===t.name&&void 0===r.branches[r.branches.length-1].condition&&n.onError(Be(30,e.loc)),n.removeNode();const i=Cn(e,t);r.branches.push(i);const o=s&&s(r,i,!1);pn(i,n),o&&o(),n.currentNode=null}else n.onError(Be(30,e.loc));break}n.removeNode(r)}}}}function Cn(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!st(e,"for")?e.children:[e],userKey:it(e,"key"),isTemplateIf:n}}function bn(e,t,n){return e.condition?Ie(e.condition,vn(e,t,n),ge(n.helper(k),['""',"true"])):vn(e,t,n)}function vn(e,t,n){const{helper:s}=n,i=_e("key",Se(`${t}`,!1,de,2)),{children:o}=e,r=o[0];if(1!==o.length||1!==r.type){if(1===o.length&&11===r.type){const e=r.codegenNode;return ut(e,i,n),e}{let t=64;return fe(n,s(A),Ee([i]),o,t+"",void 0,void 0,!0,!1,!1,e.loc)}}{const e=r.codegenNode,t=Et(e);return 13===t.type&&be(t,n),ut(t,i,n),e}}const Rn=dn("for",((e,t,n)=>{const{helper:s,removeHelper:i}=n;return xn(e,t,n,(t=>{const o=ge(s(H),[t.source]),r=lt(e),a=st(e,"memo"),c=it(e,"key"),l=c&&(6===c.type?Se(c.value.content,!0):c.exp),h=c?_e("key",l):null,p=4===t.source.type&&t.source.constType>0,d=p?64:c?128:256;return t.codegenNode=fe(n,s(A),void 0,o,d+"",void 0,void 0,!0,!p,!1,e.loc),()=>{let c;const{children:d}=t,u=1!==d.length||1!==d[0].type,f=ht(e)?e:r&&1===e.children.length&&ht(e.children[0])?e.children[0]:null;if(f?(c=f.codegenNode,r&&h&&ut(c,h,n)):u?c=fe(n,s(A),h?Ee([h]):void 0,e.children,"64",void 0,void 0,!0,void 0,!1):(c=d[0].codegenNode,r&&h&&ut(c,h,n),c.isBlock!==!p&&(c.isBlock?(i(x),i(Ce(n.inSSR,c.isComponent))):i(Ae(n.inSSR,c.isComponent))),c.isBlock=!p,c.isBlock?(s(x),s(Ce(n.inSSR,c.isComponent))):s(Ae(n.inSSR,c.isComponent))),a){const e=Ne(Mn(t.parseResult,[Se("_cached")]));e.body=Oe([Te(["const _memo = (",a.exp,")"]),Te(["if (_cached",...l?[" && _cached.key === ",l]:[],` && ${n.helperString(le)}(_cached, _memo)) return _cached`]),Te(["const _item = ",c]),Se("_item.memo = _memo"),Se("return _item")]),o.arguments.push(e,Se("_cache"),Se(String(n.cached++)))}else o.arguments.push(Ne(Mn(t.parseResult),c,!0))}}))}));function xn(e,t,n,s){if(!t.exp)return void n.onError(Be(31,t.loc));const i=t.forParseResult;if(!i)return void n.onError(Be(32,t.loc));Ln(i);const{scopes:o}=n,{source:r,value:a,key:c,index:l}=i,h={type:11,loc:t.loc,source:r,valueAlias:a,keyAlias:c,objectIndexAlias:l,parseResult:i,children:lt(e)?e.children:[e]};n.replaceNode(h),o.vFor++;const p=s&&s(h);return()=>{o.vFor--,p&&p()}}function Ln(e,t){e.finalized||(e.finalized=!0)}function Mn({value:e,key:t,index:n},s=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||Se("_".repeat(t+1),!1)))}([e,t,n,...s])}const Pn=Se("undefined",!1),Dn=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=st(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},kn=(e,t,n,s)=>Ne(e,n,!1,!0,n.length?n[0].loc:s);function Vn(e,t,n=kn){t.helper(oe);const{children:s,loc:i}=e,o=[],r=[];let a=t.scopes.vSlot>0||t.scopes.vFor>0;const c=st(e,"slot",!0);if(c){const{arg:e,exp:t}=c;e&&!Je(e)&&(a=!0),o.push(_e(e||Se("default",!0),n(t,void 0,s,i)))}let l=!1,h=!1;const p=[],d=new Set;let u=0;for(let E=0;E<s.length;E++){const e=s[E];let i;if(!lt(e)||!(i=st(e,"slot",!0))){3!==e.type&&p.push(e);continue}if(c){t.onError(Be(37,i.loc));break}l=!0;const{children:f,loc:m}=e,{arg:_=Se("default",!0),exp:S,loc:T}=i;let g;Je(_)?g=_?_.content:"default":a=!0;const N=st(e,"for"),I=n(S,N,f,m);let y,O;if(y=st(e,"if"))a=!0,r.push(Ie(y.exp,Xn(_,I,u++),Pn));else if(O=st(e,/^else(-if)?$/,!0)){let e,n=E;for(;n--&&(e=s[n],3===e.type););if(e&&lt(e)&&st(e,"if")){s.splice(E,1),E--;let e=r[r.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=O.exp?Ie(O.exp,Xn(_,I,u++),Pn):Xn(_,I,u++)}else t.onError(Be(30,O.loc))}else if(N){a=!0;const e=N.forParseResult;e?(Ln(e),r.push(ge(t.helper(H),[e.source,Ne(Mn(e),Xn(_,I),!0)]))):t.onError(Be(32,N.loc))}else{if(g){if(d.has(g)){t.onError(Be(38,T));continue}d.add(g),"default"===g&&(h=!0)}o.push(_e(_,I))}}if(!c){const e=(e,s)=>{const o=n(e,void 0,s,i);return t.compatConfig&&(o.isNonScopedSlot=!0),_e("default",o)};l?p.length&&p.some((e=>Un(e)))&&(h?t.onError(Be(39,p[0].loc)):o.push(e(void 0,p))):o.push(e(void 0,s))}const f=a?2:wn(e.children)?3:1;let m=Ee(o.concat(_e("_",Se(f+"",!1))),i);return r.length&&(m=ge(t.helper(q),[m,me(r)])),{slots:m,hasDynamicSlots:a}}function Xn(e,t,n){const s=[_e("name",e),_e("fn",t)];return null!=n&&s.push(_e("key",Se(String(n),!0))),Ee(s)}function wn(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||wn(n.children))return!0;break;case 9:if(wn(n.branches))return!0;break;case 10:case 11:if(wn(n.children))return!0}}return!1}function Un(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():Un(e.content))}const Fn=new WeakMap,Bn=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:s}=e,i=1===e.tagType;let o=i?$n(e,t):`"${n}"`;const r=h(o)&&o.callee===U;let a,c,l,p,d,u,f=0,m=r||o===C||o===b||!i&&("svg"===n||"foreignObject"===n);if(s.length>0){const n=Hn(e,t,void 0,i,r);a=n.props,f=n.patchFlag,d=n.dynamicPropNames;const s=n.directives;u=s&&s.length?me(s.map((e=>Jn(e,t)))):void 0,n.shouldUseBlock&&(m=!0)}if(e.children.length>0){o===v&&(m=!0,f|=1024);if(i&&o!==C&&o!==v){const{slots:n,hasDynamicSlots:s}=Vn(e,t);c=n,s&&(f|=1024)}else if(1===e.children.length&&o!==C){const n=e.children[0],s=n.type,i=5===s||8===s;i&&0===nn(n,t)&&(f|=1),c=i||2===s?n:e.children}else c=e.children}0!==f&&(l=String(f),d&&d.length&&(p=function(e){let t="[";for(let n=0,s=e.length;n<s;n++)t+=JSON.stringify(e[n]),n<s-1&&(t+=", ");return t+"]"}(d))),e.codegenNode=fe(t,o,a,c,l,p,u,!!m,!1,i,e.loc)};function $n(e,t,n=!1){let{tag:s}=e;const i=jn(s),o=it(e,"is",!1,!0);if(o)if(i||Xe("COMPILER_IS_ON_ELEMENT",t)){let e;if(6===o.type?e=o.value&&Se(o.value.content,!0):(e=o.exp,e||(e=Se("is",!1,o.loc))),e)return ge(t.helper(U),[e])}else 6===o.type&&o.value.content.startsWith("vue:")&&(s=o.value.content.slice(4));const r=je(s)||t.isBuiltInComponent(s);return r?(n||t.helper(r),r):(t.helper(w),t.components.add(s),mt(s,"component"))}function Hn(e,t,n=e.props,s,i,r=!1){const{tag:a,loc:c,children:h}=e;let u=[];const f=[],m=[],E=h.length>0;let _=!1,S=0,T=!1,g=!1,N=!1,I=!1,y=!1,O=!1;const A=[],C=e=>{u.length&&(f.push(Ee(Gn(u),c)),u=[]),e&&f.push(e)},b=()=>{t.scopes.vFor>0&&u.push(_e(Se("ref_for",!0),Se("true")))},v=({key:e,value:n})=>{if(Je(e)){const r=e.content,a=o(r);if(!a||s&&!i||"onclick"===r.toLowerCase()||"onUpdate:modelValue"===r||p(r)||(I=!0),a&&p(r)&&(O=!0),a&&14===n.type&&(n=n.arguments[0]),20===n.type||(4===n.type||8===n.type)&&nn(n,t)>0)return;"ref"===r?T=!0:"class"===r?g=!0:"style"===r?N=!0:"key"===r||A.includes(r)||A.push(r),!s||"class"!==r&&"style"!==r||A.includes(r)||A.push(r)}else y=!0};for(let o=0;o<n.length;o++){const i=n[o];if(6===i.type){const{loc:e,name:n,nameLoc:s,value:o}=i;let r=!0;if("ref"===n&&(T=!0,b()),"is"===n&&(jn(a)||o&&o.content.startsWith("vue:")||Xe("COMPILER_IS_ON_ELEMENT",t)))continue;u.push(_e(Se(n,!0,s),Se(o?o.content:"",r,o?o.loc:e)))}else{const{name:n,arg:o,exp:h,loc:p,modifiers:T}=i,g="bind"===n,N="on"===n;if("slot"===n){s||t.onError(Be(40,p));continue}if("once"===n||"memo"===n)continue;if("is"===n||g&&ot(o,"is")&&(jn(a)||Xe("COMPILER_IS_ON_ELEMENT",t)))continue;if(N&&r)continue;if((g&&ot(o,"key")||N&&E&&ot(o,"vue:before-update"))&&(_=!0),g&&ot(o,"ref")&&b(),!o&&(g||N)){if(y=!0,h)if(g){if(b(),C(),Xe("COMPILER_V_BIND_OBJECT_ORDER",t)){f.unshift(h);continue}f.push(h)}else C({type:14,loc:p,callee:t.helper(z),arguments:s?[h]:[h,"true"]});else t.onError(Be(g?34:35,p));continue}g&&T.includes("prop")&&(S|=32);const I=t.directiveTransforms[n];if(I){const{props:n,needRuntime:s}=I(i,e,t);!r&&n.forEach(v),N&&o&&!Je(o)?C(Ee(n,c)):u.push(...n),s&&(m.push(i),l(s)&&Fn.set(i,s))}else d(n)||(m.push(i),E&&(_=!0))}}let R;if(f.length?(C(),R=f.length>1?ge(t.helper(j),f,c):f[0]):u.length&&(R=Ee(Gn(u),c)),y?S|=16:(g&&!s&&(S|=2),N&&!s&&(S|=4),A.length&&(S|=8),I&&(S|=32)),_||0!==S&&32!==S||!(T||O||m.length>0)||(S|=512),!t.inSSR&&R)switch(R.type){case 15:let e=-1,n=-1,s=!1;for(let t=0;t<R.properties.length;t++){const i=R.properties[t].key;Je(i)?"class"===i.content?e=t:"style"===i.content&&(n=t):i.isHandlerKey||(s=!0)}const i=R.properties[e],o=R.properties[n];s?R=ge(t.helper(Y),[R]):(i&&!Je(i.value)&&(i.value=ge(t.helper(W),[i.value])),o&&(N||4===o.value.type&&"["===o.value.content.trim()[0]||17===o.value.type)&&(o.value=ge(t.helper(K),[o.value])));break;case 14:break;default:R=ge(t.helper(Y),[ge(t.helper(Q),[R])])}return{props:R,directives:m,patchFlag:S,dynamicPropNames:A,shouldUseBlock:_}}function Gn(e){const t=new Map,n=[];for(let s=0;s<e.length;s++){const i=e[s];if(8===i.key.type||!i.key.isStatic){n.push(i);continue}const r=i.key.content,a=t.get(r);a?("style"===r||"class"===r||o(r))&&qn(a,i):(t.set(r,i),n.push(i))}return n}function qn(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=me([e.value,t.value],e.loc)}function Jn(e,t){const n=[],s=Fn.get(e);s?n.push(t.helperString(s)):(t.helper(F),t.directives.add(e.name),n.push(mt(e.name,"directive")));const{loc:i}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=Se("true",!1,i);n.push(Ee(e.modifiers.map((e=>_e(e,t))),i))}return me(n,e.loc)}function jn(e){return"component"===e||"Component"===e}const Wn=(e,t)=>{if(ht(e)){const{children:n,loc:s}=e,{slotName:i,slotProps:o}=Kn(e,t),r=[t.prefixIdentifiers?"_ctx.$slots":"$slots",i,"{}","undefined","true"];let a=2;o&&(r[2]=o,a=3),n.length&&(r[3]=Ne([],n,!1,!1,s),a=4),t.scopeId&&!t.slotted&&(a=5),r.splice(a),e.codegenNode=ge(t.helper(G),r,s)}};function Kn(e,t){let n,s='"default"';const i=[];for(let o=0;o<e.props.length;o++){const t=e.props[o];if(6===t.type)t.value&&("name"===t.name?s=JSON.stringify(t.value.content):(t.name=m(t.name),i.push(t)));else if("bind"===t.name&&ot(t.arg,"name")){if(t.exp)s=t.exp;else if(t.arg&&4===t.arg.type){const e=m(t.arg.content);s=t.exp=Se(e,!1,t.arg.loc)}}else"bind"===t.name&&t.arg&&Je(t.arg)&&(t.arg.content=m(t.arg.content)),i.push(t)}if(i.length>0){const{props:s,directives:o}=Hn(e,t,i,!1,!1);n=s,o.length&&t.onError(Be(36,o[0].loc))}return{slotName:s,slotProps:n}}const Yn=/^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,Qn=(e,t,n,s)=>{const{loc:i,modifiers:o,arg:r}=e;let a;if(4===r.type)if(r.isStatic){let e=r.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`);a=Se(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?_(m(e)):`on:${e}`,!0,r.loc)}else a=Te([`${n.helperString(te)}(`,r,")"]);else a=r,a.children.unshift(`${n.helperString(te)}(`),a.children.push(")");let c=e.exp;c&&!c.content.trim()&&(c=void 0);let l=n.cacheHandlers&&!c&&!n.inVOnce;if(c){const e=tt(c.content),t=!(e||Yn.test(c.content)),n=c.content.includes(";");(t||l&&e)&&(c=Te([`${t?"$event":"(...args)"} => ${n?"{":"("}`,c,n?"}":")"]))}let h={props:[_e(a,c||Se("() => {}",!1,i))]};return s&&(h=s(h)),l&&(h.props[0].value=n.cache(h.props[0].value)),h.props.forEach((e=>e.key.isHandlerKey=!0)),h},zn=(e,t,n)=>{const{modifiers:s,loc:i}=e,o=e.arg;let{exp:r}=e;if(r&&4===r.type&&!r.content.trim()&&(r=void 0),!r){if(4!==o.type||!o.isStatic)return n.onError(Be(52,o.loc)),{props:[_e(o,Se("",!0,i))]};const t=m(o.content);r=e.exp=Se(t,!1,o.loc)}return 4!==o.type?(o.children.unshift("("),o.children.push(') || ""')):o.isStatic||(o.content=`${o.content} || ""`),s.includes("camel")&&(4===o.type?o.content=o.isStatic?m(o.content):`${n.helperString(Z)}(${o.content})`:(o.children.unshift(`${n.helperString(Z)}(`),o.children.push(")"))),n.inSSR||(s.includes("prop")&&Zn(o,"."),s.includes("attr")&&Zn(o,"^")),{props:[_e(o,r)]}},Zn=(e,t)=>{4===e.type?e.content=e.isStatic?t+e.content:`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},es=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let s,i=!1;for(let e=0;e<n.length;e++){const t=n[e];if(at(t)){i=!0;for(let i=e+1;i<n.length;i++){const o=n[i];if(!at(o)){s=void 0;break}s||(s=n[e]=Te([t],t.loc)),s.children.push(" + ",o),n.splice(i,1),i--}}}if(i&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const s=n[e];if(at(s)||8===s.type){const i=[];2===s.type&&" "===s.content||i.push(s),t.ssr||0!==nn(s,t)||i.push("1"),n[e]={type:12,content:s,loc:s.loc,codegenNode:ge(t.helper(V),i)}}}}},ts=new WeakSet,ns=(e,t)=>{if(1===e.type&&st(e,"once",!0)){if(ts.has(e)||t.inVOnce||t.inSSR)return;return ts.add(e),t.inVOnce=!0,t.helper(ne),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},ss=(e,t,n)=>{const{exp:s,arg:i}=e;if(!s)return n.onError(Be(41,e.loc)),is();const o=s.loc.source,r=4===s.type?s.content:o,a=n.bindingMetadata[o];if("props"===a||"props-aliased"===a)return is();if(!r.trim()||!tt(r))return n.onError(Be(42,s.loc)),is();const c=i||Se("modelValue",!0),l=i?Je(i)?`onUpdate:${m(i.content)}`:Te(['"onUpdate:" + ',i]):"onUpdate:modelValue";let h;h=Te([`${n.isTS?"($event: any)":"$event"} => ((`,s,") = $event)"]);const p=[_e(c,e.exp),_e(l,h)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(Ke(e)?e:JSON.stringify(e))+": true")).join(", "),n=i?Je(i)?`${i.content}Modifiers`:Te([i,' + "Modifiers"']):"modelModifiers";p.push(_e(n,Se(`{ ${t} }`,!1,e.loc,2)))}return is(p)};function is(e=[]){return{props:e}}const os=/[\w).+\-_$\]]/,rs=(e,t)=>{Xe("COMPILER_FILTERS",t)&&(5===e.type&&as(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&as(e.exp,t)})))};function as(e,t){if(4===e.type)cs(e,t);else for(let n=0;n<e.children.length;n++){const s=e.children[n];"object"==typeof s&&(4===s.type?cs(s,t):8===s.type?as(e,t):5===s.type&&as(s.content,t))}}function cs(e,t){const n=e.content;let s,i,o,r,a=!1,c=!1,l=!1,h=!1,p=0,d=0,u=0,f=0,m=[];for(o=0;o<n.length;o++)if(i=s,s=n.charCodeAt(o),a)39===s&&92!==i&&(a=!1);else if(c)34===s&&92!==i&&(c=!1);else if(l)96===s&&92!==i&&(l=!1);else if(h)47===s&&92!==i&&(h=!1);else if(124!==s||124===n.charCodeAt(o+1)||124===n.charCodeAt(o-1)||p||d||u){switch(s){case 34:c=!0;break;case 39:a=!0;break;case 96:l=!0;break;case 40:u++;break;case 41:u--;break;case 91:d++;break;case 93:d--;break;case 123:p++;break;case 125:p--}if(47===s){let e,t=o-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&os.test(e)||(h=!0)}}else void 0===r?(f=o+1,r=n.slice(0,o).trim()):E();function E(){m.push(n.slice(f,o).trim()),f=o+1}if(void 0===r?r=n.slice(0,o).trim():0!==f&&E(),m.length){for(o=0;o<m.length;o++)r=ls(r,m[o],t);e.content=r}}function ls(e,t,n){n.helper(B);const s=t.indexOf("(");if(s<0)return n.filters.add(t),`${mt(t,"filter")}(${e})`;{const i=t.slice(0,s),o=t.slice(s+1);return n.filters.add(i),`${mt(i,"filter")}(${e}${")"!==o?","+o:o}`}}const hs=new WeakSet,ps=(e,t)=>{if(1===e.type){const n=st(e,"memo");if(!n||hs.has(e))return;return hs.add(e),()=>{const s=e.codegenNode||t.currentNode.codegenNode;s&&13===s.type&&(1!==e.tagType&&be(s,t),e.codegenNode=ge(t.helper(ce),[n.exp,Ne(void 0,s),"_cache",String(t.cached++)]))}}};function ds(e){return[[ns,On,ps,Rn,rs,Wn,Bn,Dn,es],{on:Qn,bind:zn,model:ss}]}function us(e,t={}){const n=t.onError||Ue,s="module"===t.mode;!0===t.prefixIdentifiers?n(Be(47)):s&&n(Be(48));t.cacheHandlers&&n(Be(49)),t.scopeId&&!s&&n(Be(50));const i=r({},t,{prefixIdentifiers:!1}),o=c(e)?zt(e,i):e,[a,l]=ds();return hn(o,r({},i,{nodeTransforms:[...a,...t.nodeTransforms||[]],directiveTransforms:r({},l,t.directiveTransforms||{})})),mn(o,i)}const fs=()=>({props:[]}),ms=Symbol(""),Es=Symbol(""),_s=Symbol(""),Ss=Symbol(""),Ts=Symbol(""),gs=Symbol(""),Ns=Symbol(""),Is=Symbol(""),ys=Symbol(""),Os=Symbol("");let As;pe({[ms]:"vModelRadio",[Es]:"vModelCheckbox",[_s]:"vModelText",[Ss]:"vModelSelect",[Ts]:"vModelDynamic",[gs]:"withModifiers",[Ns]:"withKeys",[Is]:"vShow",[ys]:"Transition",[Os]:"TransitionGroup"});const Cs={parseMode:"html",isVoidTag:O,isNativeTag:e=>N(e)||I(e)||y(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return As||(As=document.createElement("div")),t?(As.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,As.children[0].getAttribute("foo")):(As.innerHTML=e,As.textContent)},isBuiltInComponent:e=>"Transition"===e||"transition"===e?ys:"TransitionGroup"===e||"transition-group"===e?Os:void 0,getNamespace(e,t,n){let s=t?t.ns:n;if(t&&2===s)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(s=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(s=0);else t&&1===s&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(s=0));if(0===s){if("svg"===e)return 1;if("math"===e)return 2}return s}},bs=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:Se("style",!0,t.loc),exp:vs(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},vs=(e,t)=>{const n=function(e){const t={};return e.replace(g,"").split(S).forEach((e=>{if(e){const n=e.split(T);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return Se(JSON.stringify(n),!1,t,3)};function Rs(e,t){return Be(e,t)}const xs={53:"v-html is missing expression.",54:"v-html will override element children.",55:"v-text is missing expression.",56:"v-text will override element children.",57:"v-model can only be used on <input>, <textarea> and <select> elements.",58:"v-model argument is not supported on plain elements.",59:"v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.",60:"Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.",61:"v-show is missing expression.",62:"<Transition> expects exactly one child element or component.",63:"Tags with side effect (<script> and <style>) are ignored in client component templates."},Ls=t("passive,once,capture"),Ms=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),Ps=t("left,right"),Ds=t("onkeyup,onkeydown,onkeypress",!0),ks=(e,t)=>Je(e)&&"onclick"===e.content.toLowerCase()?Se(t,!0):4!==e.type?Te(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,Vs=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Xs=[bs],ws={cloak:fs,html:(e,t,n)=>{const{exp:s,loc:i}=e;return s||n.onError(Rs(53,i)),t.children.length&&(n.onError(Rs(54,i)),t.children.length=0),{props:[_e(Se("innerHTML",!0,i),s||Se("",!0))]}},text:(e,t,n)=>{const{exp:s,loc:i}=e;return s||n.onError(Rs(55,i)),t.children.length&&(n.onError(Rs(56,i)),t.children.length=0),{props:[_e(Se("textContent",!0),s?nn(s,n)>0?s:ge(n.helperString(J),[s],i):Se("",!0))]}},model:(e,t,n)=>{const s=ss(e,t,n);if(!s.props.length||1===t.tagType)return s;e.arg&&n.onError(Rs(58,e.arg.loc));const{tag:i}=t,o=n.isCustomElement(i);if("input"===i||"textarea"===i||"select"===i||o){let r=_s,a=!1;if("input"===i||o){const s=it(t,"type");if(s){if(7===s.type)r=Ts;else if(s.value)switch(s.value.content){case"radio":r=ms;break;case"checkbox":r=Es;break;case"file":a=!0,n.onError(Rs(59,e.loc))}}else rt(t)&&(r=Ts)}else"select"===i&&(r=Ss);a||(s.needRuntime=n.helper(r))}else n.onError(Rs(57,e.loc));return s.props=s.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),s},on:(e,t,n)=>Qn(e,t,n,(t=>{const{modifiers:s}=e;if(!s.length)return t;let{key:i,value:o}=t.props[0];const{keyModifiers:r,nonKeyModifiers:a,eventOptionModifiers:c}=((e,t,n,s)=>{const i=[],o=[],r=[];for(let a=0;a<t.length;a++){const s=t[a];"native"===s&&we("COMPILER_V_ON_NATIVE",n)||Ls(s)?r.push(s):Ps(s)?Je(e)?Ds(e.content)?i.push(s):o.push(s):(i.push(s),o.push(s)):Ms(s)?o.push(s):i.push(s)}return{keyModifiers:i,nonKeyModifiers:o,eventOptionModifiers:r}})(i,s,n);if(a.includes("right")&&(i=ks(i,"onContextmenu")),a.includes("middle")&&(i=ks(i,"onMouseup")),a.length&&(o=ge(n.helper(gs),[o,JSON.stringify(a)])),!r.length||Je(i)&&!Ds(i.content)||(o=ge(n.helper(Ns),[o,JSON.stringify(r)])),c.length){const e=c.map(E).join("");i=Je(i)?Se(`${i.content}${e}`,!0):Te(["(",i,`) + "${e}"`])}return{props:[_e(i,o)]}})),show:(e,t,n)=>{const{exp:s,loc:i}=e;return s||n.onError(Rs(61,i)),{props:[],needRuntime:n.helper(Is)}}};return e.BASE_TRANSITION=R,e.BindingTypes={DATA:"data",PROPS:"props",PROPS_ALIASED:"props-aliased",SETUP_LET:"setup-let",SETUP_CONST:"setup-const",SETUP_REACTIVE_CONST:"setup-reactive-const",SETUP_MAYBE_REF:"setup-maybe-ref",SETUP_REF:"setup-ref",OPTIONS:"options",LITERAL_CONST:"literal-const"},e.CAMELIZE=Z,e.CAPITALIZE=ee,e.CREATE_BLOCK=L,e.CREATE_COMMENT=k,e.CREATE_ELEMENT_BLOCK=M,e.CREATE_ELEMENT_VNODE=D,e.CREATE_SLOTS=q,e.CREATE_STATIC=X,e.CREATE_TEXT=V,e.CREATE_VNODE=P,e.CompilerDeprecationTypes={COMPILER_IS_ON_ELEMENT:"COMPILER_IS_ON_ELEMENT",COMPILER_V_BIND_SYNC:"COMPILER_V_BIND_SYNC",COMPILER_V_BIND_OBJECT_ORDER:"COMPILER_V_BIND_OBJECT_ORDER",COMPILER_V_ON_NATIVE:"COMPILER_V_ON_NATIVE",COMPILER_V_IF_V_FOR_PRECEDENCE:"COMPILER_V_IF_V_FOR_PRECEDENCE",COMPILER_NATIVE_TEMPLATE:"COMPILER_NATIVE_TEMPLATE",COMPILER_INLINE_TEMPLATE:"COMPILER_INLINE_TEMPLATE",COMPILER_FILTERS:"COMPILER_FILTERS"},e.ConstantTypes={NOT_CONSTANT:0,0:"NOT_CONSTANT",CAN_SKIP_PATCH:1,1:"CAN_SKIP_PATCH",CAN_HOIST:2,2:"CAN_HOIST",CAN_STRINGIFY:3,3:"CAN_STRINGIFY"},e.DOMDirectiveTransforms=ws,e.DOMErrorCodes={X_V_HTML_NO_EXPRESSION:53,53:"X_V_HTML_NO_EXPRESSION",X_V_HTML_WITH_CHILDREN:54,54:"X_V_HTML_WITH_CHILDREN",X_V_TEXT_NO_EXPRESSION:55,55:"X_V_TEXT_NO_EXPRESSION",X_V_TEXT_WITH_CHILDREN:56,56:"X_V_TEXT_WITH_CHILDREN",X_V_MODEL_ON_INVALID_ELEMENT:57,57:"X_V_MODEL_ON_INVALID_ELEMENT",X_V_MODEL_ARG_ON_ELEMENT:58,58:"X_V_MODEL_ARG_ON_ELEMENT",X_V_MODEL_ON_FILE_INPUT_ELEMENT:59,59:"X_V_MODEL_ON_FILE_INPUT_ELEMENT",X_V_MODEL_UNNECESSARY_VALUE:60,60:"X_V_MODEL_UNNECESSARY_VALUE",X_V_SHOW_NO_EXPRESSION:61,61:"X_V_SHOW_NO_EXPRESSION",X_TRANSITION_INVALID_CHILDREN:62,62:"X_TRANSITION_INVALID_CHILDREN",X_IGNORED_SIDE_EFFECT_TAG:63,63:"X_IGNORED_SIDE_EFFECT_TAG",__EXTEND_POINT__:64,64:"__EXTEND_POINT__"},e.DOMErrorMessages=xs,e.DOMNodeTransforms=Xs,e.ElementTypes={ELEMENT:0,0:"ELEMENT",COMPONENT:1,1:"COMPONENT",SLOT:2,2:"SLOT",TEMPLATE:3,3:"TEMPLATE"},e.ErrorCodes={ABRUPT_CLOSING_OF_EMPTY_COMMENT:0,0:"ABRUPT_CLOSING_OF_EMPTY_COMMENT",CDATA_IN_HTML_CONTENT:1,1:"CDATA_IN_HTML_CONTENT",DUPLICATE_ATTRIBUTE:2,2:"DUPLICATE_ATTRIBUTE",END_TAG_WITH_ATTRIBUTES:3,3:"END_TAG_WITH_ATTRIBUTES",END_TAG_WITH_TRAILING_SOLIDUS:4,4:"END_TAG_WITH_TRAILING_SOLIDUS",EOF_BEFORE_TAG_NAME:5,5:"EOF_BEFORE_TAG_NAME",EOF_IN_CDATA:6,6:"EOF_IN_CDATA",EOF_IN_COMMENT:7,7:"EOF_IN_COMMENT",EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT:8,8:"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT",EOF_IN_TAG:9,9:"EOF_IN_TAG",INCORRECTLY_CLOSED_COMMENT:10,10:"INCORRECTLY_CLOSED_COMMENT",INCORRECTLY_OPENED_COMMENT:11,11:"INCORRECTLY_OPENED_COMMENT",INVALID_FIRST_CHARACTER_OF_TAG_NAME:12,12:"INVALID_FIRST_CHARACTER_OF_TAG_NAME",MISSING_ATTRIBUTE_VALUE:13,13:"MISSING_ATTRIBUTE_VALUE",MISSING_END_TAG_NAME:14,14:"MISSING_END_TAG_NAME",MISSING_WHITESPACE_BETWEEN_ATTRIBUTES:15,15:"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES",NESTED_COMMENT:16,16:"NESTED_COMMENT",UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME:17,17:"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME",UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE:18,18:"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE",UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME:19,19:"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME",UNEXPECTED_NULL_CHARACTER:20,20:"UNEXPECTED_NULL_CHARACTER",UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME:21,21:"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME",UNEXPECTED_SOLIDUS_IN_TAG:22,22:"UNEXPECTED_SOLIDUS_IN_TAG",X_INVALID_END_TAG:23,23:"X_INVALID_END_TAG",X_MISSING_END_TAG:24,24:"X_MISSING_END_TAG",X_MISSING_INTERPOLATION_END:25,25:"X_MISSING_INTERPOLATION_END",X_MISSING_DIRECTIVE_NAME:26,26:"X_MISSING_DIRECTIVE_NAME",X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END:27,27:"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END",X_V_IF_NO_EXPRESSION:28,28:"X_V_IF_NO_EXPRESSION",X_V_IF_SAME_KEY:29,29:"X_V_IF_SAME_KEY",X_V_ELSE_NO_ADJACENT_IF:30,30:"X_V_ELSE_NO_ADJACENT_IF",X_V_FOR_NO_EXPRESSION:31,31:"X_V_FOR_NO_EXPRESSION",X_V_FOR_MALFORMED_EXPRESSION:32,32:"X_V_FOR_MALFORMED_EXPRESSION",X_V_FOR_TEMPLATE_KEY_PLACEMENT:33,33:"X_V_FOR_TEMPLATE_KEY_PLACEMENT",X_V_BIND_NO_EXPRESSION:34,34:"X_V_BIND_NO_EXPRESSION",X_V_ON_NO_EXPRESSION:35,35:"X_V_ON_NO_EXPRESSION",X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET:36,36:"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET",X_V_SLOT_MIXED_SLOT_USAGE:37,37:"X_V_SLOT_MIXED_SLOT_USAGE",X_V_SLOT_DUPLICATE_SLOT_NAMES:38,38:"X_V_SLOT_DUPLICATE_SLOT_NAMES",X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN:39,39:"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN",X_V_SLOT_MISPLACED:40,40:"X_V_SLOT_MISPLACED",X_V_MODEL_NO_EXPRESSION:41,41:"X_V_MODEL_NO_EXPRESSION",X_V_MODEL_MALFORMED_EXPRESSION:42,42:"X_V_MODEL_MALFORMED_EXPRESSION",X_V_MODEL_ON_SCOPE_VARIABLE:43,43:"X_V_MODEL_ON_SCOPE_VARIABLE",X_V_MODEL_ON_PROPS:44,44:"X_V_MODEL_ON_PROPS",X_INVALID_EXPRESSION:45,45:"X_INVALID_EXPRESSION",X_KEEP_ALIVE_INVALID_CHILDREN:46,46:"X_KEEP_ALIVE_INVALID_CHILDREN",X_PREFIX_ID_NOT_SUPPORTED:47,47:"X_PREFIX_ID_NOT_SUPPORTED",X_MODULE_MODE_NOT_SUPPORTED:48,48:"X_MODULE_MODE_NOT_SUPPORTED",X_CACHE_HANDLER_NOT_SUPPORTED:49,49:"X_CACHE_HANDLER_NOT_SUPPORTED",X_SCOPE_ID_NOT_SUPPORTED:50,50:"X_SCOPE_ID_NOT_SUPPORTED",X_VNODE_HOOKS:51,51:"X_VNODE_HOOKS",X_V_BIND_INVALID_SAME_NAME_ARGUMENT:52,52:"X_V_BIND_INVALID_SAME_NAME_ARGUMENT",__EXTEND_POINT__:53,53:"__EXTEND_POINT__"},e.FRAGMENT=A,e.GUARD_REACTIVE_PROPS=Q,e.IS_MEMO_SAME=le,e.IS_REF=ae,e.KEEP_ALIVE=v,e.MERGE_PROPS=j,e.NORMALIZE_CLASS=W,e.NORMALIZE_PROPS=Y,e.NORMALIZE_STYLE=K,e.Namespaces={HTML:0,0:"HTML",SVG:1,1:"SVG",MATH_ML:2,2:"MATH_ML"},e.NodeTypes={ROOT:0,0:"ROOT",ELEMENT:1,1:"ELEMENT",TEXT:2,2:"TEXT",COMMENT:3,3:"COMMENT",SIMPLE_EXPRESSION:4,4:"SIMPLE_EXPRESSION",INTERPOLATION:5,5:"INTERPOLATION",ATTRIBUTE:6,6:"ATTRIBUTE",DIRECTIVE:7,7:"DIRECTIVE",COMPOUND_EXPRESSION:8,8:"COMPOUND_EXPRESSION",IF:9,9:"IF",IF_BRANCH:10,10:"IF_BRANCH",FOR:11,11:"FOR",TEXT_CALL:12,12:"TEXT_CALL",VNODE_CALL:13,13:"VNODE_CALL",JS_CALL_EXPRESSION:14,14:"JS_CALL_EXPRESSION",JS_OBJECT_EXPRESSION:15,15:"JS_OBJECT_EXPRESSION",JS_PROPERTY:16,16:"JS_PROPERTY",JS_ARRAY_EXPRESSION:17,17:"JS_ARRAY_EXPRESSION",JS_FUNCTION_EXPRESSION:18,18:"JS_FUNCTION_EXPRESSION",JS_CONDITIONAL_EXPRESSION:19,19:"JS_CONDITIONAL_EXPRESSION",JS_CACHE_EXPRESSION:20,20:"JS_CACHE_EXPRESSION",JS_BLOCK_STATEMENT:21,21:"JS_BLOCK_STATEMENT",JS_TEMPLATE_LITERAL:22,22:"JS_TEMPLATE_LITERAL",JS_IF_STATEMENT:23,23:"JS_IF_STATEMENT",JS_ASSIGNMENT_EXPRESSION:24,24:"JS_ASSIGNMENT_EXPRESSION",JS_SEQUENCE_EXPRESSION:25,25:"JS_SEQUENCE_EXPRESSION",JS_RETURN_STATEMENT:26,26:"JS_RETURN_STATEMENT"},e.OPEN_BLOCK=x,e.POP_SCOPE_ID=ie,e.PUSH_SCOPE_ID=se,e.RENDER_LIST=H,e.RENDER_SLOT=G,e.RESOLVE_COMPONENT=w,e.RESOLVE_DIRECTIVE=F,e.RESOLVE_DYNAMIC_COMPONENT=U,e.RESOLVE_FILTER=B,e.SET_BLOCK_TRACKING=ne,e.SUSPENSE=b,e.TELEPORT=C,e.TO_DISPLAY_STRING=J,e.TO_HANDLERS=z,e.TO_HANDLER_KEY=te,e.TRANSITION=ys,e.TRANSITION_GROUP=Os,e.TS_NODE_TYPES=qe,e.UNREF=re,e.V_MODEL_CHECKBOX=Es,e.V_MODEL_DYNAMIC=Ts,e.V_MODEL_RADIO=ms,e.V_MODEL_SELECT=Ss,e.V_MODEL_TEXT=_s,e.V_ON_WITH_KEYS=Ns,e.V_ON_WITH_MODIFIERS=gs,e.V_SHOW=Is,e.WITH_CTX=oe,e.WITH_DIRECTIVES=$,e.WITH_MEMO=ce,e.advancePositionWithClone=function(e,t,n=t.length){return nt({offset:e.offset,line:e.line,column:e.column},t,n)},e.advancePositionWithMutation=nt,e.assert=function(e,t){if(!e)throw new Error(t||"unexpected compiler condition")},e.baseCompile=us,e.baseParse=zt,e.buildDirectiveArgs=Jn,e.buildProps=Hn,e.buildSlots=Vn,e.checkCompatEnabled=we,e.compile=function(e,t={}){return us(e,r({},Cs,t,{nodeTransforms:[Vs,...Xs,...t.nodeTransforms||[]],directiveTransforms:r({},ws,t.directiveTransforms||{}),transformHoist:null}))},e.convertToBlock=be,e.createArrayExpression=me,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:de}},e.createBlockStatement=Oe,e.createCacheExpression=ye,e.createCallExpression=ge,e.createCompilerError=Be,e.createCompoundExpression=Te,e.createConditionalExpression=Ie,e.createDOMCompilerError=Rs,e.createForLoopParams=Mn,e.createFunctionExpression=Ne,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:de}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:c(e)?Se(e,!1,t):e}},e.createObjectExpression=Ee,e.createObjectProperty=_e,e.createReturnStatement=function(e){return{type:26,returns:e,loc:de}},e.createRoot=ue,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:de}},e.createSimpleExpression=Se,e.createStructuralDirectiveTransform=dn,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:de}},e.createTransformContext=ln,e.createVNodeCall=fe,e.errorMessages=$e,e.extractIdentifiers=He,e.findDir=st,e.findProp=it,e.forAliasRE=_t,e.generate=mn,e.generateCodeFrame=function(e,t=0,n=e.length){let s=e.split(/(\r?\n)/);const i=s.filter(((e,t)=>t%2==1));s=s.filter(((e,t)=>t%2==0));let o=0;const r=[];for(let a=0;a<s.length;a++)if(o+=s[a].length+(i[a]&&i[a].length||0),o>=t){for(let e=a-2;e<=a+2||n>o;e++){if(e<0||e>=s.length)continue;const c=e+1;r.push(`${c}${" ".repeat(Math.max(3-String(c).length,0))}|  ${s[e]}`);const l=s[e].length,h=i[e]&&i[e].length||0;if(e===a){const e=t-(o-(l+h)),s=Math.max(1,n>o?l-e:n-t);r.push("   |  "+" ".repeat(e)+"^".repeat(s))}else if(e>a){if(n>o){const e=Math.max(Math.min(n-o,l),1);r.push("   |  "+"^".repeat(e))}o+=l+h}}break}return r.join("\n")},e.getBaseTransformPreset=ds,e.getConstantType=nn,e.getMemoedVNodeCall=Et,e.getVNodeBlockHelper=Ce,e.getVNodeHelper=Ae,e.hasDynamicKeyVBind=rt,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let s=0;s<t.props.length;s++){const i=t.props[s];if(7===i.type&&(e(i.arg,n)||e(i.exp,n)))return!0}return t.children.some((t=>e(t,n)));case 11:return!!e(t.source,n)||t.children.some((t=>e(t,n)));case 9:return t.branches.some((t=>e(t,n)));case 10:return!!e(t.condition,n)||t.children.some((t=>e(t,n)));case 4:return!t.isStatic&&Ke(t.content)&&!!n[t.content];case 8:return t.children.some((t=>h(t)&&e(t,n)));case 5:case 12:return e(t.content,n);default:return!1}},e.helperNameMap=he,e.injectProp=ut,e.isCoreComponent=je,e.isFunctionType=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),e.isInDestructureAssignment=function(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){const n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1},e.isInNewExpression=function(e){let t=e.length;for(;t--;){const n=e[t];if("NewExpression"===n.type)return!0;if("MemberExpression"!==n.type)break}return!1},e.isMemberExpression=tt,e.isMemberExpressionBrowser=Ze,e.isMemberExpressionNode=et,e.isReferencedIdentifier=function(e,t,n){return!1},e.isSimpleIdentifier=Ke,e.isSlotOutlet=ht,e.isStaticArgOf=ot,e.isStaticExp=Je,e.isStaticProperty=Ge,e.isStaticPropertyKey=(e,t)=>Ge(t)&&t.key===e,e.isTemplateNode=lt,e.isText=at,e.isVSlot=ct,e.locStub=de,e.noopDirectiveTransform=fs,e.parse=function(e,t={}){return zt(e,r({},Cs,t))},e.parserOptions=Cs,e.processExpression=yn,e.processFor=xn,e.processIf=An,e.processSlotOutlet=Kn,e.registerRuntimeHelpers=pe,e.resolveComponentType=$n,e.stringifyExpression=function e(t){return c(t)?t:4===t.type?t.content:t.children.map(e).join("")},e.toValidAssetId=mt,e.trackSlotScopes=Dn,e.trackVForSlotScopes=(e,t)=>{let n;if(lt(e)&&e.props.some(ct)&&(n=st(e,"for"))){const e=n.forParseResult;if(e){Ln(e);const{value:n,key:s,index:i}=e,{addIdentifiers:o,removeIdentifiers:r}=t;return n&&o(n),s&&o(s),i&&o(i),()=>{n&&r(n),s&&r(s),i&&r(i)}}}},e.transform=hn,e.transformBind=zn,e.transformElement=Bn,e.transformExpression=(e,t)=>{if(5===e.type)e.content=yn(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){const s=e.props[n];if(7===s.type&&"for"!==s.name){const e=s.exp,n=s.arg;!e||4!==e.type||"on"===s.name&&n||(s.exp=yn(e,t,"slot"===s.name)),n&&4===n.type&&!n.isStatic&&(s.arg=yn(n,t))}}},e.transformModel=ss,e.transformOn=Qn,e.transformStyle=bs,e.traverseNode=pn,e.unwrapTSNode=function e(t){return qe.includes(t.type)?e(t.expression):t},e.walkBlockDeclarations=function(e,t){for(const n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(const e of n.declarations)for(const n of He(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}else if("ForOfStatement"===n.type||"ForInStatement"===n.type||"ForStatement"===n.type){const e="ForStatement"===n.type?n.init:n.left;if(e&&"VariableDeclaration"===e.type)for(const n of e.declarations)for(const e of He(n.id))t(e)}},e.walkFunctionParams=function(e,t){for(const n of e.params)for(const e of He(n))t(e)},e.walkIdentifiers=function(e,t,n=!1,s=[],i=Object.create(null)){},e.warnDeprecation=function(e,t,n,...s){if("suppress-warning"===Ve(e,t))return;const{message:i,link:o}=ke[e],r=`(deprecation ${e}) ${"function"==typeof i?i(...s):i}${o?`\n  Details: ${o}`:""}`,a=new SyntaxError(r);a.code=e,n&&(a.loc=n),t.onWarn(a)},e}({});
